--liquibase formatted sql

--changeset a0866112:001 runOnChange:true
create or replace view V_OTP_QSN as
select Q.TX_TYPE,
  Q.TX_ID,
  Q.CREATED_DATE,
  Q.QSN_DATA,
  Q.QSN_DATA.userInfo.email as USER_EMAIL,
  Q.QSN_DATA.userInfo.firstname as USER_FIRST_NAME,
  Q.QSN_DATA.userInfo.lastname as USER_LAST_NAME,
  Q.QSN_DATA."order".opn as ORDER_OPN,
  Q.QSN_DATA."order"."otp" as ORDER_OTP,
  Q.PRICE,
  <PERSON>.OPN_NEW
from OTP_QSN Q
left join V_OTP_REQ_LATEST_APP R
  on R.TX_TYPE = Q.TX_TYPE and R.TX_ID = Q.TX_ID and R.SRC_APP = 'GALILEO';
--rollback drop view V_OTP_QSN;
