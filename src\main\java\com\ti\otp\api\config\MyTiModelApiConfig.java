package com.ti.otp.api.config;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "app.my-ti-model-api")
public class MyTiModelApiConfig {
  private boolean enabled;
  private String baseUrl;

  @PostConstruct
  public void run() {
    log.info("myTiModelApi enabled = {}", enabled);
    if (enabled) {
      log.debug("myTiModelApi base URL = {}", baseUrl);
    }
  }
}
