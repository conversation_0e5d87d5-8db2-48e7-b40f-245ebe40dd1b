--liquibase formatted sql

--changeset a0866112:001
create table OTP_TASKSVC_CFG (
  UUID raw(16) default sys_guid() not null,
  SRC_APP varchar2(40) not null,
  APP_UUID raw(16) not null,
  TMPL_UUID raw(16) not null,
  TGT_APP varchar2(40) not null,
  SUCCESS_STATE varchar2(40) not null,
  FAILURE_STATE varchar2(40) not null
);
--rollback drop table OTP_TASKSVC_CFG;

--changeset a0866112:002
alter table OTP_TASKSVC_CFG add constraint OTP_TASKSVC_CFG_PK primary key (UUID);
--rollback alter table OTP_TASKSVC_CFG drop constraint OTP_TASKSVC_CFG_PK;

--changeset a0866112:003
alter table OTP_TASKSVC_CFG add constraint OTP_TASKSVC_CFG_UK unique (TMPL_UUID);
--rollback alter table OTP_TASKSVC_CFG drop constraint OTP_TASKSVC_CFG_UK;

--changeset a0866112:004
create index OTP_TASKSVC_CFG_SRC_APP on OTP_TASKSVC_CFG (SRC_APP);
--rollback drop index OTP_TASKSVC_CFG_SRC_APP;

--changeset a0866112:005
create index OTP_TASKSVC_CFG_TGT_APP on OTP_TASKSVC_CFG (TGT_APP);
--rollback drop index OTP_TASKSVC_CFG_TGT_APP;

--changeset a0461144:006
alter table OTP_TASKSVC_CFG add INIT_BRANCH varchar2(40);
--rollback alter table OTP_TASKSVC_CFG drop column INIT_BRANCH;

--changeset a0461144:007
alter table OTP_TASKSVC_CFG add INBOUND_FILTER_SUCCESS varchar2(1);
--rollback alter table OTP_TASKSVC_CFG drop column INBOUND_FILTER_SUCCESS;

