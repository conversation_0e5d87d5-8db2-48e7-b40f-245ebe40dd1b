server.port=8080
server.error.include-message=always

spring.datasource.url=*******************************************************************
spring.datasource.username=TPLDAPP1
spring.datasource.password=GX94GSAg2ECInmzh47

spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ssXXX
spring.jackson.time-zone=US/Central

mybatis.configuration.map-underscore-to-camel-case=true
mybatis.type-aliases-package=com.ti.otp.api.domain.**
mybatis.configuration.jdbc-type-for-null=NULL

springdoc.default-produces-media-type=application/json
springdoc.swagger-ui.operationsSorter=alpha

ti.security.header.consumer.uidHeaderName=X-ZUUL-TI-UID
ti.security.header.consumer.roleHeaderName=X-ZUUL-TI-ROLE
ti.security.header.consumer.secretHeaderName=X-ZUUL-TI-SECRET
ti.security.header.consumer.secret=MY_SECRET
ti.security.header.consumer.permitUrls=/actuator/**,/v3/api-docs/**,/v3/api-docs.yaml,/swagger-ui.html,/swagger-ui/**,/favicon.ico,/test.htm,/uuids/generate,/requests/send

logging.level.com.ti.spring.boot.security.headerconsumer.autoconfigure=error

# Whether to enable APIs that write to the database
app.api.writable=true

# Spring mail properties
spring.mail.host=smtp.mail.ti.com
spring.mail.properties.mail.smtp.sendpartial=true

# Whether to enable APIs that write to the database
app.default-tx-type=TPLD_CUSTOM_OPN
app.tasksvc.tmplUuid=08B7EF7DC7F0360CE0633768AC0ADB90
app.tasksvc.initBranch=Start Review
app.tasksvc.marketingFunction=Marketing

# The myTiModel API is used for lookup of ww account id
app.my-ti-model-api.enabled=false
app.my-ti-model-api.base-url=https://ticom-webservice-int.ext.ti.com/mytimodel/api

# Properties for WW Account ID Refresh
app.ww-acc-id.refresh.cron.expression=0 0 0 * * *
app.ww-acc-id.refresh.api.enabled=true
app.ww-acc-id.refresh.api.url=https://dmboomi-sit.itg.ti.com:9093/ws/rest/myti/mytidata
app.ww-acc-id.refresh.api.username=mf_app@texasinstrumentsincorpora-VVE2HT
app.ww-acc-id.refresh.api.password=8b07660c-584a-4e4d-9c96-537c2a5ee78e

# Properties for email for support team, in case of errors
app.ertp.listen-failure.email.enabled=true
app.ertp.listen-failure.email.from=<EMAIL>
app.ertp.listen-failure.email.tos=<EMAIL>
app.ertp.listen-failure.email.subject=ERTP TPLD Spring Service Failure

# Properties for follow-up email for new customers
app.new-cust.setup.email.enabled=false
app.new-cust.setup.email.from=<EMAIL>
app.new-cust.setup.email.tos=<EMAIL>,<EMAIL>,<EMAIL>
app.new-cust.setup.email.subject=New customer setup required for TPLD [DEV]
app.new-cust.setup.email.template=new_cust_setup_email_template.html
app.new-cust.setup.email.days-before-follow-up=2

# Whether to enable Kafka integration
app.kafka.enabled=true
app.kafka.bootstrap-servers=lelvrpmfgd01.itg.ti.com:9092,lelvrpmfgd02.itg.ti.com:9092,lelvrpmfgd03.itg.ti.com:9092
app.kafka.security.protocol=SASL_SSL
app.kafka.sasl.mechanism=SCRAM-SHA-512
app.kafka.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="global_tpld" password="SqLHSYE9nFC/";
app.kafka.consumer.group-id=otp-dev
app.kafka.topic.inbound=internal.opnautomation.flow.1.0.application.ertp.out
app.kafka.topic.outbound=internal.opnautomation.flow.1.0.action.start-tx
app.kafka.filter-type=TPLD_CUSTOM_OPN
app.kafka.filter-app=ERTP
app.kafka.filter-status=''
app.kafka.filter-success=true
app.kafka.store-inbound=true

# Whether to poll database for records to send to Kafka
app.db-polling.enabled=false
app.db-polling.interval-ms=10000
app.db-polling.source-app=NONE
app.db-polling.source-status=''

app.tasksvc.enabled=true
app.tasksvc.basePath=https://api-dev.itg.ti.com
app.tasksvc.tokenUrl=/v1/oauth/
app.tasksvc.tokenClientId=XBkyU7AuYsk7N8tS8tew47Fmj6Skkb3s
app.tasksvc.tokenClientSecret=rBtA9yzoPkkauy71
app.tasksvc.userId=a0866112

eloqua.enabled=true
eloqua.basePath=https://ticom-webservice-int.ext.ti.com
eloqua.userId=transactional-email-tpld
eloqua.password=Ko(G9@qyi

# TPLD Email Properties
eloqua.environment=INT
eloqua.buyUrl=<a href='https://www.ti.com/ordering-resources/buy.html'>TI.com-Ordering resources</a>
eloqua.inline-programming-url=<a href='https://TBD.ti.com'>In-line programming</a>
eloqua.gang-programming-url=<a href='https://www.ti.com/lit/ug/slau358q/slau358q.pdf?ts=1698157514881&ref_url=https%253A%252F%252Fwww.ti.com%252Fsitesearch%252Fen-us%252Fdocs%252Funiversalsearch.tsp%253FlangPref%253Den-US%2526searchTerm%253Dmsp%2Bgang%2Bprogrammer%2526nr%253D2160'>Gang programmer</a>
eloqua.customer-support-url=<a href='https://support.ti.com/csm?keyMatch=CUSTOMER%20SUPPORT%20CENTER'>TI Customer Support Center</a>
eloqua.high-volume-threshold-weeks=3-4
eloqua.high-volume-threshold-units=250
eloqua.completion-high-volume-event-id=118039
eloqua.completion-high-volume-event-hash=eyJhcGlCYXNpY0F1dGhDcmVkZW50aWFsSWQiOiAxMzI2fQ==
eloqua.approval-high-volume-event-id=117787
eloqua.approval-high-volume-event-hash=eyJhcGlCYXNpY0F1dGhDcmVkZW50aWFsSWQiOiAxMzIxfQ==
eloqua.approval-low-volume-event-id=118034
eloqua.approval-low-volume-event-hash=eyJhcGlCYXNpY0F1dGhDcmVkZW50aWFsSWQiOiAxMzI0fQ==
eloqua.rejection-event-id=118038
eloqua.rejection-event-hash=eyJhcGlCYXNpY0F1dGhDcmVkZW50aWFsSWQiOiAxMzI1fQ==
eloqua.high-volume-business-days=5
eloqua.low-volume-business-weeks=2