package com.ti.otp.api.service;

import com.ti.otp.api.domain.OtpReq;
import com.ti.otp.api.domain.OtpTaskSvcCfg;
import com.ti.otp.api.repository.OtpDao;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OtpService {

  public static final String TPLD_CUSTOM_OPN_TX_TYPE = "TPLD_CUSTOM_OPN";

  @Autowired
  private OtpDao otpDao;

  public String createReq(OtpReq req) {
    otpDao.createReq(req);
    return req.getUuid();
  }

  public String generateUuid() {
    return otpDao.generateUuid();
  }

  public OtpReq getLatestReqByTxId(String txId) {
    return otpDao.getLatestReqByTxId(txId);
  }

  public OtpReq getReqByTxIdApp(String txId, String srcApp) {
    return otpDao.getReqByTxIdApp(txId, srcApp);
  }

  public OtpReq getReqByUuid(String uuid) {
    return otpDao.getReqByUuid(uuid);
  }

  public List<OtpReq> getReqsByTxId(String txId) {
    return otpDao.getReqsByTxId(txId);
  }

  public List<OtpReq> getUnsentReqsByAppStatus(String srcApp, String status) {
    return otpDao.getUnsentReqsByAppStatus(srcApp, status);
  }

  public String getOtpCfgByOpnNew(String opnNew) {
    return otpDao.getOtpCfgByOpnNew(opnNew);
  }

  public String getOtpCfgByTxTypeAndOpnNew(String txType, String opnNew) {
    // Get the otp-cfg from different sources based on tx type
    if(txType.equalsIgnoreCase(TPLD_CUSTOM_OPN_TX_TYPE)){
      return otpDao.getOtpCfgByOpnNewFromQsn(opnNew);
    }

    // TODO: Add additional source for different txType if needed

    return null;
  }

  public OtpTaskSvcCfg getTaskSvcCfgBySrcApp(String srcApp) {
    return otpDao.getTaskSvcCfgBySrcApp(srcApp);
  }

  public OtpTaskSvcCfg getTaskSvcCfgByTmplUuid(String tmplUuid) {
    return otpDao.getTaskSvcCfgByTmplUuid(tmplUuid);
  }

  public void markReqAsSent(String uuid) {
    log.debug("Marking as sent: {}", uuid);
    otpDao.markReqAsSent(uuid);
  }
}
