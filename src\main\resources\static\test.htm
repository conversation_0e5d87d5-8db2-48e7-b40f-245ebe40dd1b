<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252" />
    <title>OTP-API Tester</title>
    <style type="text/css">
      * {
        font-family: "Roboto", "Franklin Gothic Medium", Tahoma, sans-serif;
        font-size: 1rem;
        line-height: 1.1;
        color: #6c7379;
        background-color: #e2e6ec;
      }
      th,
      td {
        padding: 0.3em;
      }
      th {
        text-align: left;
      }
      th.fieldName {
        min-width: 250px;
      }
      th.fieldValue {
        min-width: 500px;
      }
      input,
      select,
      textarea {
        border: 1px solid transparent;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
        background-color: #fff;
        border-color: #babcbf;
      }
      hr {
        border: 1px dashed #6c7379;
      }
      button {
        color: #fff;
        background-color: #2163c4;
        border: 1px solid transparent;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
      }
      button:disabled, button[disabled] {
        background-color: #999999;
      }
      .width-wide {
        width: 100%;
      }
      .width-nowrap {
        white-space: nowrap;
      }
      .centralized {
        text-align: right;
      }
      .requiredLabel::after {
        content: " *";
        font-size: 0.83em;
        vertical-align: super;
      }
      .requiredLabel.noValue {
        color: #cc0000;
      }
      .jsonStatus {
        font-size: small;
        margin-top: 0.25rem;
      }
      .jsonStatus.invalidJson {
        color: #cc0000;
      }
    </style>
    <script type="text/javascript">
      const ALL_FIELDS = [
        "txId",
        "txType",
        "srcApp",
        "success",
        "errMsg",
        "opnOrig",
        "opnNew",
        "opnNewOldMatl",
        "opnNewGpn",
        "status",
        "otpCfg",
      ];

      function checkPreview() {
        const value = document.getElementById("msgPreview").value;
        let validated = false;
        if (value != null && value !== "") {
          try {
            JSON.parse(value);
            document.getElementById("btnSubmit").disabled = false;
            validated = true;
          } catch (error) {
            validated = false;
          }
        }
        document.getElementById("btnSubmit").disabled = !validated;
        document.getElementById("btnParse").disabled = !validated;
        const divValidPreview = document.getElementById("divValidPreview");
        divValidPreview.innerText = (validated ? "Valid" : "Invalid") + " JSON";
        divValidPreview.classList.toggle("invalidJson", !validated);
        return validated;
      }

      function checkRequired() {
        let validated = true;
        const success = document.getElementById("successTrue").checked;
        const errMsg = document.getElementById("errMsg");
        document.getElementById("errMsgLabel").classList.toggle("requiredLabel", !success);
        if (success) {
          errMsg.value = "";
        }
        errMsg.disabled = success;
        const opnOrig = document.getElementById("opnOrig").value;
        document.getElementById("btnGenerateNewOpn").disabled = (opnOrig == null || opnOrig === "");
        ALL_FIELDS.forEach((field) => {
          document.getElementsByName(field).forEach((element) => {
            const label = document.getElementById(field + "Label");
            if (label) {
              const noValue = element.value == null || element.value === "";
              label.classList.toggle("noValue", noValue);
              if (label.classList.contains("requiredLabel") && noValue) {
                validated = false;
              }
            }
          });
        });
        document.getElementById("btnPreview").disabled = !validated;
        return validated;
      }

      function generateNewOpn() {
        const opnOrig = document.getElementById("opnOrig").value;
        if (opnOrig != null && opnOrig !== "") {
          const suffixDigits = 6;
          const randomNum = Math.floor(
            Math.random() * Math.pow(16, suffixDigits)
          );
          const suffix = randomNum
            .toString(16)
            .toUpperCase()
            .padStart(suffixDigits, "0");
          document.getElementById("opnNew").value = opnOrig + suffix;
          checkRequired();
        } else {
          alert("Please specify Original OPN");
        }
      }

      function generateUuid() {
        document.getElementById("txId").value = crypto.randomUUID();
        checkRequired();
      }

      function parse() {
        let jsonObj = {};
        try {
          jsonObj = JSON.parse(document.getElementById("msgPreview").value);
        } catch (error) {}
        ALL_FIELDS.forEach((field) => {
          document.getElementsByName(field).forEach((element) => {
            if (jsonObj[field] != null) {
              if (element.type === "radio") {
                if (element.value == String(jsonObj[field])) {
                  element.checked = true;
                }
              } else {
                element.value = jsonObj[field];
              }
            }
          });
        });
        checkRequired();
      }

      function preview() {
        if (!checkRequired()) {
          alert("Please specify all required values");
          return false;
        }
        const jsonObj = {};
        ALL_FIELDS.forEach((field) => {
          document.getElementsByName(field).forEach((element) => {
            let value = element.value;
            if (element.type === "radio") {
              if (element.checked) {
                jsonObj[field] = (value == "true");
              }
            } else {
              const noValue = (value == null || value === "");
              if (!noValue) {
                if (field.startsWith("opn")) {
                  value = String(value).toUpperCase();
                }
                jsonObj[field] = value;
              }
            }
          });
        });
        document.getElementById("msgPreview").value = JSON.stringify(
          jsonObj,
          null,
          2
        );
        checkPreview();
        return true;
      }

      function submit() {
        if (!checkRequired()) {
          alert("Please specify all required values");
          return false;
        }
        const url = "./requests/send";
        const body = document.getElementById("msgPreview").value;
        try {
          fetch(url, {
            method: "POST",
            cache: "no-cache",
            headers: {
              "Content-Type": "application/json",
            },
            body: body,
          })
            .then((response) => {
              if (response.ok) {
                response.text().then((data) => {
                  alert("Message has been sent to topic: " + data);
                });
              } else {
                throw Error(
                  "Server responded with status code " + response.status
                );
              }
            })
            .catch((error) => {
              console.error(error);
              alert(error);
            });
        } catch (error) {
          console.error(error);
          alert("Error while sending message:\n" + error);
        }
        return true;
      }
    </script>
  </head>
  <body onload="checkRequired()">
    <table>
      <thead>
        <tr>
          <th class="fieldName">Field (Key)</th>
          <th class="fieldValue">Value</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td id="txIdLabel" class="requiredLabel noValue">
            Request ID (txId)
          </td>
          <td class="width-nowrap">
            <input
              type="text"
              id="txId"
              name="txId"
              size="35"
              maxlength="32"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
            />
            <button id="btnGenerateUuid" onclick="generateUuid()">Generate</button>
          </td>
        </tr>
        <tr>
          <td id="txTypeLabel" class="requiredLabel">Request Type (txType)</td>
          <td>
            <input
              type="text"
              id="txType"
              name="txType"
              value="TPLD_CUSTOM_OPN"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
              class="width-wide"
            />
          </td>
        </tr>
        <tr>
          <td id="srcAppLabel" class="requiredLabel">Source App (srcApp)</td>
          <td>
            <select
              id="srcApp"
              name="srcApp"
              onchange="checkRequired()"
              class="width-wide"
            >
              <option value="TICOM">TICOM</option>
              <option value="GALILEO">GALILEO</option>
              <option value="SAP">SAP</option>
              <option value="SCS">SCS</option>
              <option value="PCS">PCS</option>
              <option value="ATSS">ATSS</option>
              <option value="ERTP">ERTP</option>
            </select>
          </td>
        </tr>
        <tr>
          <td id="successLabel" class="requiredLabel">Success (success)</td>
          <td>
            <input
              type="radio"
              value="true"
              id="successTrue"
              name="success"
              checked=""
              onchange="checkRequired()"
            /><label for="successTrue">true</label>
            <input
              type="radio"
              value="false"
              id="successFalse"
              name="success"
              onchange="checkRequired()"
            /><label for="successFalse">false</label>
          </td>
        </tr>
        <tr>
          <td id="errMsgLabel" class="noValue">Error Message (errMsg)</td>
          <td>
            <input
              type="text"
              id="errMsg"
              name="errMsg"
              size="40"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
              class="width-wide"
            />
          </td>
        </tr>
        <tr>
          <td id="opnOrigLabel" class="requiredLabel noValue">
            Original OPN (opnOrig)
          </td>
          <td>
            <input
              type="text"
              id="opnOrig"
              name="opnOrig"
              size="35"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
              class="width-wide"
            />
          </td>
        </tr>
        <tr>
          <td id="opnNewLabel" class="noValue">New OPN (opnNew)</td>
          <td class="width-nowrap">
            <input
              type="text"
              id="opnNew"
              name="opnNew"
              size="35"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
            />
            <button id="btnGenerateNewOpn" onclick="generateNewOpn()">Generate</button>
          </td>
        </tr>
        <tr>
          <td id="opnNewOldMatlLabel" class="noValue">New OPN's Old Material Name (opnNewOldMatl)</td>
          <td class="width-nowrap">
            <input
              type="text"
              id="opnNewOldMatl"
              name="opnNewOldMatl"
              size="35"
              maxlength="14"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
            />
          </td>
        </tr>
        <tr>
          <td id="opnNewGpnLabel" class="noValue">New OPN's GPN (opnNewGpn)</td>
          <td class="width-nowrap">
            <input
              type="text"
              id="opnNewGpn"
              name="opnNewGpn"
              size="35"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
            />
          </td>
        </tr>
        <tr>
          <td id="statusLabel" class="noValue">Status Message (status)</td>
          <td>
            <input
              type="text"
              id="status"
              name="status"
              size="35"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
              class="width-wide"
            />
          </td>
        </tr>
        <tr>
          <td id="otpCfgLabel" class="noValue">OTP Config (otpCfg)</td>
          <td>
            <textarea
              id="otpCfg"
              name="otpCfg"
              rows="5"
              onkeyup="checkRequired()"
              onchange="checkRequired()"
              class="width-wide"
            ></textarea>
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td>
            <hr />
          </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td class="centralized">
            <button id="btnPreview" onclick="preview()" disabled="">
              Preview Message &#9660;
            </button>
            <button id="btnParse" onclick="parse()" disabled="">
              Parse Message &#9650;
            </button>
          </td>
        </tr>
        <tr>
          <td>Message Preview</td>
          <td>
            <textarea
              id="msgPreview"
              name="msgPreview"
              rows="10"
              onkeyup="checkPreview()"
              onchange="checkPreview()"
              class="width-wide"
            ></textarea>
            <div class="jsonStatus invalidJson" id="divValidPreview">
              Invalid JSON
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2" class="centralized">
            <button id="btnSubmit" onclick="submit()" disabled="">
              Submit Message
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
