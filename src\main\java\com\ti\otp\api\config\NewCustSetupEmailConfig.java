package com.ti.otp.api.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@ConfigurationProperties(prefix = "app.new-cust.setup.email")
@Getter
@Setter
@Slf4j
public class NewCustSetupEmailConfig extends GenericEmailConfig{
    private String template;

    @PostConstruct
    public void run(){
        log.info("New Customer Setup Email enabled: {}", super.isEnabled());
        if(super.isEnabled()){
            log.debug("New Customer Setup Email days before follow up: {}", super.getDaysBeforeFollowUp());
        }
    }
}
