package com.ti.otp.api.service;

import com.ti.otp.api.config.WwAccIdLookupApiConfig;
import com.ti.otp.api.domain.wwAccIdLookup.WwAccIdLookupResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotBlank;

@Service
@Slf4j
public class WwAccIdLookupApiService {

    @Autowired
    private WwAccIdLookupApiConfig apiConfig;
    private final RestTemplate restTemplate = new RestTemplate();
    public static final String INVALID_WW_ACCT_ID_PREFIX = "000000999";
    public static final String INVALID_WW_ACCT_SOLD_TO = "0000000000";

    private HttpHeaders generateHeaders(){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(apiConfig.getUsername(), apiConfig.getPassword());
        return headers;
    }

    private String generateRequestBody(String email){
        return "{\"email\": \"" + email + "\"}";
    }

    public boolean isValidWwAccId(String wwAccId){
        return StringUtils.hasLength(wwAccId)
                && !wwAccId.equals(INVALID_WW_ACCT_SOLD_TO)
                && !wwAccId.startsWith(INVALID_WW_ACCT_ID_PREFIX);
    }

    public boolean isValidSoldTo(String soldTo){
        return StringUtils.hasLength(soldTo) && !soldTo.equals(INVALID_WW_ACCT_SOLD_TO);
    }

    public WwAccIdLookupResponse callApi(@NotBlank String email){
        if(!apiConfig.isEnabled()){
            return null;
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(generateRequestBody(email), generateHeaders());
        try{
            // API Call
            ResponseEntity<WwAccIdLookupResponse> response = restTemplate.exchange(
                    apiConfig.getUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    WwAccIdLookupResponse.class
            );

            if(response.getStatusCode() == HttpStatus.OK){
                WwAccIdLookupResponse successResponse = response.getBody();
                log.debug("WW Account ID lookup success response: {}", successResponse);
                return successResponse;
            }
        }
        // For 4xx errors
        catch(HttpClientErrorException ex){
            log.debug("Client error response status code: {}", ex.getStatusCode());
            log.debug("Response body: {}", ex.getResponseBodyAsString());
            return null;
        }
        // For 5xx errors
        catch (HttpServerErrorException ex){
            log.debug("Server error response status code: {}", ex.getStatusCode());
            log.debug("Response body: {}", ex.getResponseBodyAsString());
            return null;
        }
        catch(Exception ex){
            log.debug("Unexpected error occurred: {}", ex.getMessage());
            return null;
        }

        // default null
        return null;
    }

}
