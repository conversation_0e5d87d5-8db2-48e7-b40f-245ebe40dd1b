### Task 1: Adding "Order Type" Column to the Dashboard

#### Objective
Add a new column named **"Order Type"** to the dashboard.

#### Steps Taken

1. **Frontend Analysis**  
   - Identified that the dashboard screen was implemented in `ReqTable.js`.
   - Determined that changes were required in this file to add the new column.
   - Traced the data source for the dashboard, which was being loaded from the API endpoint:  
     `/tpld-api/questionnaires/TPLD_CUSTOM_OPN`.

2. **Backend Exploration**  
   - Located the API implementation in the backend. The endpoint `/questionnaires/{txType}` was defined in `QsnController.java`.
   - Investigated the service layer (`OtpQsnService.java`) and DAO layer (`OtpQsnDao.xml`) to understand how the data was being fetched.
   - Found that the data was being retrieved from the view `V_OTP_QSN`, aliased as `QSN`.

3. **Database Analysis**  
   - Traced the view `V_OTP_QSN` to its underlying table `OTP_QSN` in the file `view-questionnaire.sql`.
   - Logged into the database using Toad to inspect the `OTP_QSN` table.
   - Verified that the table's attributes matched the dashboard data for confirmation.
   - Found that the `orderType` information was nested within the `QSN_DATA` column under the structure:  
     `order -> questionnaire -> orderType`.

4. **Implementation**  
   - Added a new column in `ReqTable.js` with the following properties:  
     - **Header**: "Order Type"  
     - **Field**: `qsnData.order.questionnaire.orderType`.

#### Outcome
The "Order Type" column was successfully added to the dashboard, displaying the required data from the backend.

---

### Task 2: Renaming "Estimated Volume" Field on the Individual Request Page

#### Objective
Rename the **"Estimated Volume"** field to **"Yearly Estimated Volume"** on the individual request page.

#### Steps Taken

1. **Frontend Analysis**  
   - Identified that the individual request page was implemented in `ReqView.js`.
   - Located the **"Estimated Volume"** field in the file.

2. **Implementation**  
   - Renamed the field from **"Estimated Volume"** to **"Yearly Estimated Volume"** in `ReqView.js`.
   - Verified that the field was fetching data from `questionnaire.volumeEst`, which is nested in the `QSN_DATA` column under the structure:  
     `order -> questionnaire -> volumeEst`.

#### Outcome
The field was successfully renamed to **"Yearly Estimated Volume"** on the individual request page.

---

### Task 3: Making WW Account ID Optional for "samples" Order Type

#### Objective
Allow task creation for **"samples"** order type without requiring the **WW Account ID**. For other order types, the **WW Account ID** remains mandatory.

#### Steps Taken

1. **Backend Analysis**  
   - Identified that the API endpoint `/questionnaires` was responsible for creating questionnaires.  
   - Located the implementation in the `QsnController.java` file under the method:  
     `@PostMapping("/questionnaires")`.

2. **Code Exploration**  
   - Found that the `createQsn` method in `OtpQsnService.java` was responsible for:  
     - Saving the questionnaire in the database.  
     - Looking up the **WW Account ID** and updating the database.  
     - Sending an email.  
     - Creating a task if the **WW Account ID** was present.  
   - Verified that the **order type** was stored in the `qsnData` field of the `OtpQsn` object, under the structure:  
     `qsnData -> order -> questionnaire -> orderType`.


3. **Implementation**  
   - Extracted the **order type** from the `qsnData` field in the `createQsn` method.  
   - Added an `if-else` condition to handle task creation:  
     - If the **order type** is **"samples"**, the task is created without requiring the **WW Account ID**.  
     - For other order types, the existing logic remains unchanged, requiring the **WW Account ID** for task creation.  
   - Ensured that the **WW Account ID** lookup and database update still occur for all order types.

#### Outcome
The feature was successfully implemented, allowing task creation for **"samples"** order type without requiring the **WW Account ID**, while maintaining the existing behavior for other order types.
