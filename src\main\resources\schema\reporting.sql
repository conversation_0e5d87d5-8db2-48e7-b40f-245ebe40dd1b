--liquibase formatted sql

--changeset a0866112:001
create table OTP_SRC_APP_CFG (
  TX_TYPE varchar2(40) not null,
  SRC_APP varchar2(40) not null,
  SRC_APP_ORDER number(3,0) not null
);
--rollback drop table OTP_SRC_APP_CFG;

--changeset a0866112:002 runOnChange:true
merge into OTP_SRC_APP_CFG TGT
using (
  select TX_TYPE, SRC_APP, SRC_APP_ORDER from (
    select 'TPLD_CUSTOM_OPN' as TX_TYPE, '["TICOM","GALILEO","SAP","SCS","ATSS","ERTP","PCS"]' as SRC_APPS_JSON from dual
  ) SRC, json_table(SRC_APPS_JSON, '$[*]' columns (SRC_APP varchar2(40) path '$', SRC_APP_ORDER for ordinality)) JT
) SRC
on (TGT.TX_TYPE = SRC.TX_TYPE and TGT.SRC_APP = SRC.SRC_APP)
when matched then
  update set TGT.SRC_APP_ORDER = SRC.SRC_APP_ORDER
    where TGT.SRC_APP_ORDER != SRC.SRC_APP_ORDER
when not matched then
  insert (TGT.TX_TYPE, TGT.SRC_APP, TGT.SRC_APP_ORDER)
  values (SRC.TX_TYPE, SRC.SRC_APP, SRC.SRC_APP_ORDER);
--rollback delete from OTP_SRC_APP_CFG;

--changeset a0866112:003 runAlways:true runOnChange:true endDelimiter:~
begin
  for x in (
    select * from (
      select TABLE_NAME from user_tables union select VIEW_NAME from user_views
    ) cross join (
      select USERNAME from all_users where USERNAME = 'RO_SPOTFIRE'
    ) where TABLE_NAME like '%OTP%' order by 1
  )
  loop
    execute immediate 'grant select on ' || x.TABLE_NAME || ' to ' || x.USERNAME;
  end loop;
end;
~
--rollback select 'N/A' from dual;

--changeset a0866112:004
alter table OTP_SRC_APP_CFG add constraint OTP_SRC_APP_CFG_PK primary key (TX_TYPE, SRC_APP);
--rollback alter table OTP_SRC_APP_CFG drop constraint OTP_SRC_APP_CFG_PK;
