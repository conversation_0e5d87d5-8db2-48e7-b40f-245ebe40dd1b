package com.ti.otp.api.service;

import com.ti.otp.api.config.EloquaEmailConfig;
import com.ti.otp.api.config.ListenFailureEmailConfig;
import com.ti.otp.api.domain.EloquaEmail;
import com.ti.otp.api.domain.EloquaErrorException;
import com.ti.otp.api.domain.InvalidTxIdException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.mail.MessagingException;

@Service
@Slf4j
public class EloquaEmailService {

    @Autowired
    private EloquaEmailConfig eloquaEmailConfig;

    @Autowired
    private EmailService emailService;

    @Autowired
    private ListenFailureEmailConfig listenFailureEmailConfig;

    RestTemplate restTemplate = new RestTemplate();

    public ResponseEntity<String> sendEloquaMail(EloquaEmail eloquaEmail, String eventId, String eventHash) throws MessagingException, EloquaErrorException {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(eloquaEmailConfig.getUserId(), eloquaEmailConfig.getPassword());

        HttpEntity<Object> requestEntity = new HttpEntity<>(eloquaEmail, headers);

        ResponseEntity<String> responseEntity = null;

        try {
            responseEntity = restTemplate.exchange(
                    eloquaEmailConfig.getBasePath() + "/transactionalemails/tpld/" + eventId + "/" + eventHash,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
        }

        catch (HttpClientErrorException.Unauthorized e) {
            String errMsg = e.getMessage();
            String logMsg = "Encountered the following authorization error: "+ errMsg + ", while processing the request: "+eloquaEmail;

            emailService.send(logMsg, listenFailureEmailConfig);
            throw new EloquaErrorException(errMsg);
        }

        catch (Exception e) {
            String errMsg = e.toString();
            String logMsg = "Encountered the following error: "+ errMsg + ", while processing the request: "+eloquaEmail;

            emailService.send(logMsg, listenFailureEmailConfig);
            throw new EloquaErrorException(errMsg);
        }

        log.info("Response status recieved {}", responseEntity.getStatusCode());
        log.info("Response body recieved {}", responseEntity.getBody());

        return responseEntity;
    }
}
