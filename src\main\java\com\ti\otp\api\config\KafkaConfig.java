package com.ti.otp.api.config;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.util.StringUtils;
import com.ti.otp.api.domain.OtpReq;

@ConditionalOnProperty(value = "app.kafka.enabled")
@Configuration
public class KafkaConfig {
 
  @Value(value = "${app.kafka.bootstrap-servers}")
  private String bootstrapAddress;
 
  @Value(value = "${app.kafka.security.protocol}")
  private String securityProtocol;

  @Value(value = "${app.kafka.sasl.mechanism}")
  private String saslMechanism;

  @Value(value = "${app.kafka.sasl.jaas.config}")
  private String saslJaasConfig;

  @Bean
  public ProducerFactory<String, Object> producerFactory() {
    Map<String, Object> producerConfigs = new HashMap<>();
    producerConfigs.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    producerConfigs.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    producerConfigs.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    if (isNotBlank(securityProtocol)) {
      producerConfigs.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
    }
    if (isNotBlank(saslMechanism)) {
      producerConfigs.put(SaslConfigs.SASL_MECHANISM, saslMechanism);
    }
    if (isNotBlank(saslJaasConfig)) {
      producerConfigs.put(SaslConfigs.SASL_JAAS_CONFIG, saslJaasConfig);
    }

    return new DefaultKafkaProducerFactory<>(producerConfigs);
  }

  @Bean
  public KafkaTemplate<String, Object> kafkaTemplate() {
    return new KafkaTemplate<>(producerFactory());
  }

  @Bean
  public ConsumerFactory<String, OtpReq> consumerFactory() {
    JsonDeserializer<OtpReq> deserializer = new JsonDeserializer<>(OtpReq.class, false);
    deserializer.addTrustedPackages("*");
    Map<String, Object> consumerConfigs = new HashMap<>();
    consumerConfigs.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    if (isNotBlank(securityProtocol)) {
      consumerConfigs.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
    }
    if (isNotBlank(saslMechanism)) {
      consumerConfigs.put(SaslConfigs.SASL_MECHANISM, saslMechanism);
    }
    if (isNotBlank(saslJaasConfig)) {
      consumerConfigs.put(SaslConfigs.SASL_JAAS_CONFIG, saslJaasConfig);
    }

    return new DefaultKafkaConsumerFactory<>(
        consumerConfigs,
        new ErrorHandlingDeserializer<>(new StringDeserializer()),
        new ErrorHandlingDeserializer<>(deserializer));
  }

  @Bean
  public ConcurrentKafkaListenerContainerFactory<String, OtpReq>
      kafkaListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, OtpReq> kafkaListenerContainerFactory =
        new ConcurrentKafkaListenerContainerFactory<>();
    kafkaListenerContainerFactory.setConsumerFactory(consumerFactory());
    return kafkaListenerContainerFactory;
  }

  private static boolean isNotBlank(String value) {
    return StringUtils.hasLength(value);
  }

}
