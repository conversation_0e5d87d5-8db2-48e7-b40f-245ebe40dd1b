package com.ti.otp.api.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.ti.otp.api.config.EloquaEmailConfig;
import com.ti.otp.api.config.ListenFailureEmailConfig;
import com.ti.otp.api.config.TaskSvcConfig;
import com.ti.otp.api.domain.*;
import com.ti.otp.api.repository.OtpQsnDao;
import com.ti.tasksvc.client.model.TaskMetaUpsertForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ti.otp.api.kafka.KafkaMessageProducer;
import com.ti.otp.api.repository.OtpDao;
import com.ti.tasksvc.client.api.TaskApiApi;
import com.ti.tasksvc.client.api.TemplateApiApi;
import com.ti.tasksvc.client.invoker.ApiClient;
import com.ti.tasksvc.client.model.ODataCollectionTaskBase;
import com.ti.tasksvc.client.model.TaskBase;
import com.ti.tasksvc.client.model.TaskCreateForm;
import com.ti.tasksvc.client.model.TaskUpdateForm;
import com.ti.tasksvc.client.model.TemplateFunction;

import lombok.extern.slf4j.Slf4j;

import javax.mail.MessagingException;

@Service
@Slf4j
public class TaskSvcService {
  private static final String CTX_ID_LABEL = "txId";
  private static final String TPLD_CUSTOM_OPN_TYPE = "TPLD_CUSTOM_OPN";

  @Autowired
  private ApiClient apiClient;

  @Autowired
  private OtpDao otpDao;

  @Autowired
  private OtpQsnDao otpQsnDao;

  @Autowired
  private EloquaEmailConfig eloquaEmailConfig;

  @Autowired
  private EloquaEmailService eloquaEmailService;

  @Value(value = "${eloqua.approval-high-volume-event-id}")
  private String approvalHighVolumeEventId;

  @Value(value = "${eloqua.approval-high-volume-event-hash}")
  private String approvalHighVolumeEventHash;

  @Value(value = "${eloqua.approval-low-volume-event-id}")
  private String approvalLowVolumeEventId;

  @Value(value = "${eloqua.approval-low-volume-event-hash}")
  private String approvalLowVolumeEventHash;

  @Value(value = "${eloqua.rejection-event-id}")
  private String rejectionEventId;

  @Value(value = "${eloqua.rejection-event-hash}")
  private String rejectionEventHash;

  @Value(value = "${eloqua.environment}")
  private String environment;

  @Value(value = "${eloqua.high-volume-business-days}")
  private String highVolumeBusinessDays;

  @Value(value = "${eloqua.low-volume-business-weeks}")
  private String lowVolumeBusinessWeeks;

  @Value(value = "${eloqua.inline-programming-url}")
  private String inlineProgrammingUrl;

  @Value(value = "${eloqua.gang-programming-url}")
  private String gangProgrammingUrl;

  @Value(value = "${eloqua.customer-support-url}")
  private String customerSupportUrl;

  @Autowired
  private TaskSvcConfig taskSvcConfig;

  @Autowired
  private KafkaMessageProducer kafkaMessageProducer;

  @Autowired
  private EmailService emailService;

  @Autowired
  private ListenFailureEmailConfig listenFailureEmailConfig;

  private ObjectMapper objectMapper = new ObjectMapper();

  public TaskBase getTaskByUuid(String taskUuid) {
    TaskApiApi api = new TaskApiApi(apiClient);
    TaskBase task = api.getTaskByUuid(taskUuid).block();
    return task;
  }

  public boolean processCallback(TaskSvcTriggerRequest triggerReq) throws UnknownException, KafkaErrorException, MessagingException {
    String taskUuid = triggerReq.getTaskUuid();
    String stateName = triggerReq.getStateName();
    TaskBase task = getTaskByUuid(taskUuid);
    if (task == null) {
      log.error("Cannot find task using UUID = {}", taskUuid);
      return false;
    }
    String tmplUuid = task.getTmplUuid();
    OtpTaskSvcCfg cfg = otpDao.getTaskSvcCfgByTmplUuid(tmplUuid);
    if (cfg == null) {
      log.warn("Cannot find mapping for TaskSvc template = {}", tmplUuid);
      return false;
    }
    String taskDesc = task.getTaskDesc();
    OtpReq otpReq = null;
    try {
      otpReq = objectMapper.readValue(taskDesc, OtpReq.class);
    } catch (Exception e) {
      log.error("Cannot retrieve request JSON from task = {}", taskUuid);
      return false;
    }
    boolean isSuccess = cfg.getSuccessState().equalsIgnoreCase(stateName);
    boolean isFailure = cfg.getFailureState().equalsIgnoreCase(stateName);
    if (isSuccess || isFailure) {
      log.debug("Task moved to {} state: {} = {}", (isSuccess ? "success" : "failure"), taskUuid, stateName);
      otpReq.setSrcApp(cfg.getTgtApp());
      otpReq.setSuccess(isSuccess);
      otpReq.setStatus(stateName);
      kafkaMessageProducer.sendReq(otpReq);
      return true;
    } else {
      log.warn("Task moved to unmapped state: {} = {}", taskUuid, stateName);
      return false;
    }
  }

  private TaskMetaUpsertForm prepareNonEditableTaskMeta(String attrName, String attrValue){
    TaskMetaUpsertForm metaForm = new TaskMetaUpsertForm();
    metaForm.attrName(attrName);
    metaForm.attrValue(attrValue);
    metaForm.isEditable(false);
    return metaForm;
  }

  private List<TaskMetaUpsertForm> prepareTaskMetaListFromQsn(OtpQsn qsn){
    if(qsn == null){
      return null;
    }

    List<TaskMetaUpsertForm> metaList = new ArrayList<>();
    metaList.add(prepareNonEditableTaskMeta("price", qsn.getPrice()));
    metaList.add(prepareNonEditableTaskMeta("wwAccountId", qsn.getWwAccountId()));
    metaList.add(prepareNonEditableTaskMeta("customerEmail", qsn.getQsnData().at("/userInfo/email").asText()));
    return metaList;
  }

  public void processRequest(OtpReq req) {
    log.debug("Processing request = {}", req);
    String srcApp = req.getSrcApp();
    OtpTaskSvcCfg cfg = getMappedTaskSvcCfg(srcApp);
    if (cfg != null && cfg.getTmplUuid() != null) {
      String tmplUuid = cfg.getTmplUuid();
      String initBranch = cfg.getInitBranch();

      Boolean inboundFilterSuccess = cfg.getInboundFilterSuccess();
      boolean ignoreSuccess = (inboundFilterSuccess == null);
      boolean matchSuccess = ignoreSuccess || (inboundFilterSuccess.booleanValue() == req.isSuccess());
      if (matchSuccess) {
        try {
          String reqJson = objectMapper.writeValueAsString(req);
          TaskApiApi api = new TaskApiApi(apiClient);
          TaskCreateForm form = new TaskCreateForm();
          form.tmplUuid(tmplUuid);
          form.taskName(req.getOpnNew());
          form.taskDesc(reqJson);
          form.ctxIdLabel("txId");
          form.ctxIdValue(req.getTxId());

          // Task metadata
          OtpQsn qsn = getQsnDetails(req);
          List<TaskMetaUpsertForm> metaList = prepareTaskMetaListFromQsn(qsn);
          if(metaList != null){
            form.metaList(metaList);
          }

          log.debug("Creating TaskSvc task = {}", form);
          TaskBase result = api.createTask(form).block();
          String taskUuid = result.getTaskUuid();
          log.debug("Created new task = {}", taskUuid);

          // make the initial state change if initBranch configured
          if (StringUtils.hasLength(initBranch)) {
            TaskUpdateForm update = new TaskUpdateForm();
            update.branchName(initBranch);
            log.debug("Promoting TaskSvc task {}", taskUuid);
            TaskBase updated = api.updateTask(taskUuid, update).block();
            String newState = updated.getStateName();
            log.debug("Promoted TaskSvc task {} to state {}", taskUuid, newState);
          }
        } catch (Exception e) {
          log.error("Cannot process request due to error: ", e);
        }
      } else {
        log.debug("Inbound message does not match task creation filter");
      }
    } else {
      log.debug("Source app not mapped for task service = {}", srcApp);
    }
  }

  private OtpTaskSvcCfg getMappedTaskSvcCfg(String srcApp) {
    return otpDao.getTaskSvcCfgBySrcApp(srcApp);
  }

  private OtpQsn getQsnDetails(OtpReq req){
    return otpQsnDao.getQsnsByTypeId(req.getTxType(), req.getTxId());
  }

  public TaskBase createTask(OtpQsn qsn) {
    TaskBase result = null;
    if (isEnabled()) {
      String txId = qsn.getTxId();
      String tmplUuid = taskSvcConfig.getTmplUuid();
      String initBranch = taskSvcConfig.getInitBranch();
      try {
        TaskApiApi api = new TaskApiApi(apiClient);

        TaskCreateForm create = new TaskCreateForm();
        create.tmplUuid(tmplUuid);
        create.taskName(txId);
        create.ctxIdLabel(CTX_ID_LABEL);
        create.ctxIdValue(txId);

        log.debug("Creating TaskSvc task for txId = {}", txId);
        TaskBase created = api.createTask(create).block();
        String taskUuid = created.getTaskUuid();
        log.debug("Created TaskSvc task {}", taskUuid);
        result = created;

        if (StringUtils.hasLength(initBranch)) {
          TaskUpdateForm update = new TaskUpdateForm();
          update.branchName(initBranch);

          log.debug("Promoting TaskSvc task {}", taskUuid);
          TaskBase updated = api.updateTask(taskUuid, update).block();
          String newState = updated.getStateName();
          log.debug("Promoted TaskSvc task {} to state {}", taskUuid, newState);
          result = updated;
        }
      } catch (Exception e) {
        log.error("Cannot process TaskSvc task due to error: ", e);
      }
    }
    return result;
  }

  public List<TaskBase> findTasks(OtpQsn qsn) {
    if (isEnabled()) {
      String txId = qsn.getTxId();
      String tmplUuid = taskSvcConfig.getTmplUuid();
      try {
        TaskApiApi api = new TaskApiApi(apiClient);

        TaskCreateForm create = new TaskCreateForm();
        create.tmplUuid(tmplUuid);
        create.taskName(txId);
        create.ctxIdLabel(CTX_ID_LABEL);
        create.ctxIdValue(txId);

        log.debug("Finding TaskSvc tasks for txId = {}", txId);
        ODataCollectionTaskBase response = api.getTasks(null, null, null, tmplUuid, null, null, null, CTX_ID_LABEL, txId, null, null, null, null, null, null, null, null, null, null, null, null).block();
        List<TaskBase> tasks = response.getValue();
        log.debug("Found TaskSvc tasks {}", tasks);
        if (tasks != null) {
          return tasks;
        }
      } catch (Exception e) {
        log.error("Cannot find TaskSvc tasks due to error: ", e);
      }
    }
    return Collections.emptyList();
  }

  public List<String> getFunctionUserIds(String fnctName) {
    if (isEnabled()) {
      try {
        TemplateApiApi api = new TemplateApiApi(apiClient);
        List<TemplateFunction> fncts = api.getFunctions(taskSvcConfig.getTmplUuid()).block().getValue();
        if (fncts != null) {
          Optional<TemplateFunction> fnct = fncts.stream().filter(item -> fnctName.equals(item.getFnctName())).findAny();
          if (fnct.isPresent()) {
            Object dataSrcCfg = fnct.get().getDataSrcCfg();
            if (dataSrcCfg instanceof Map) {
              Object userIdList = ((Map<?,?>)dataSrcCfg).get("userIdList");
              if (userIdList instanceof List) {
                List<?> userIds = (List<?>)userIdList;
                return userIds.stream().map(Object::toString).collect(Collectors.toList());
              }
            }
          }
        }
      } catch (Exception e) {
        log.error("Cannot retrieve template functions: ", e);
      }
    }
    return Collections.emptyList();
  }

  public List<String> getMarketingUserIds() {
    String fnctName = taskSvcConfig.getMarketingFunction();
    return getFunctionUserIds(fnctName);
  }

  public boolean isEnabled() {
    String tmplUuid = taskSvcConfig.getTmplUuid();
    return taskSvcConfig.isEnabled() && StringUtils.hasLength(tmplUuid);
  }

  public boolean isMarketingUser(String userId) {
    String fnctName = taskSvcConfig.getMarketingFunction();
    List<String> functionUsers = otpQsnDao.getFunctionUsers(fnctName);
    if (functionUsers != null && functionUsers.contains(userId)) {
      return true;
    }
    return getMarketingUserIds().contains(userId);
  }

  public TaskSvcTriggerResponse evaluateMarketingReviewProceedOrNotForTPLD(JsonNode jsonData){
    JsonNode taskNameNode = jsonData.get("taskName");
    // Field is either absent or null
    if(taskNameNode == null || taskNameNode.isNull()){
      return TaskSvcTriggerResponse.builder()
              .proceed(Boolean.FALSE)
              .reason("Task name for the task is missing in before trigger request!")
              .build();
    }
    else{
      String txId = taskNameNode.asText();
      // Find QSN by TPLD_CUSTOM_OPN txType and txId
      OtpQsn qsn = otpQsnDao.getQsnsByTypeId(TPLD_CUSTOM_OPN_TYPE, txId);
      if(qsn == null){
        return TaskSvcTriggerResponse.builder()
                .proceed(Boolean.FALSE)
                .reason("No questionnaire found for the transaction represented by the task!")
                .build();
      }
      else if(qsn.getWwAccountId() == null){
        return TaskSvcTriggerResponse.builder()
                .proceed(Boolean.FALSE)
                .reason("No WW Account ID found in the customer questionnaire for the transaction! Please verify if the customer has WW Account ID in TI.com")
                .build();
      }
      else{
        return TaskSvcTriggerResponse.builder()
                .proceed(Boolean.TRUE)
                .reason("Success! WW Account ID found!")
                .build();
      }
    }
  }

  public String triggerCallback(TaskSvcTriggerRequest triggerReq) throws UnknownException, KafkaErrorException, MessagingException, EloquaErrorException, InvalidTxIdException, DbErrorException {

    String eventId = "";
    String eventHash = "";

    OtpQsn otpQsn = null;

    try {
      otpQsn = otpQsnDao.getQsnsByTypeId("TPLD_CUSTOM_OPN", triggerReq.getCtxIdValue());
    }

    catch (Exception e) {
      String errMsg = e.getMessage();
      String logMsg = "Encountered the following DB connection error: " + errMsg + ", while processing the request: " + triggerReq;

      log.error(logMsg);
      emailService.send(logMsg, listenFailureEmailConfig);
      throw new DbErrorException(e.getMessage());
    }

    if (Objects.isNull(otpQsn)) {
      String errMsg = "Invalid Tx Id. Didn't find required fields from questionnaire for the given Tx Id";
      String logMsg = "Encountered the following error: "+ errMsg + ", while processing the request: " + triggerReq;

      log.error(logMsg);
      emailService.send(logMsg, listenFailureEmailConfig);
      throw new InvalidTxIdException(errMsg);
    }

    String branchName = triggerReq.getBranchName();

    OtpReq otpReq = new OtpReq();

    if (branchName.equals("Approve")) {
      otpReq.setTxId(otpQsn.getTxId());
      otpReq.setTxType(otpQsn.getTxType());
      otpReq.setOpnOrig(otpQsn.getOpnOrig());
      otpReq.setSrcApp("TICOM");
      otpReq.setStatus("Approved");
      otpReq.setSuccess(true);
      otpReq.setFlowName("TPLD");
      otpReq.setOpnNewType("Custom"); // All OPNs generated by TPLD are Custom OPNs
      String txData = "{\"wwAccountId\":\"" + otpQsn.getWwAccountId() + "\", \"companyName\":\"" + otpQsn.getCompanyName() + "\", \"customerEmail\":\"" + otpQsn.getEmailTo() + "\", \"price\":\"" + otpQsn.getPrice() + "\", \"otpCfg\":\"" + otpQsn.getOpnCfg().replace("\n","\\n") + "\", \"xchain\":\"\"" + "}";
      otpReq.setTxData(txData);

      kafkaMessageProducer.sendStartTxReq(otpReq);

      return "Send to Start Tx Kafka";
    }

    else if (branchName.equals("Approve HV") ||  branchName.equals("Approve LV") || branchName.equals("Reject")) {

      EloquaEmail eloquaEmail = new EloquaEmail();

      eloquaEmail.setEmailTo(otpQsn.getEmailTo());
      eloquaEmail.setFirstName(otpQsn.getFirstName());
      eloquaEmail.setEnvironment(environment);

        switch (branchName) {
          // Not applicable anymore. Keeping for possible future change
//          case "Approve HV":
//            eloquaEmail.setBusinessDays(highVolumeBusinessDays);
//            eloquaEmail.setCustomerSupportUrl(customerSupportUrl);
//
//            eventId = approvalHighVolumeEventId;
//            eventHash = approvalHighVolumeEventHash;

//            break;

          // Not applicable anymore. Keeping for possible future change
//          case "Approve LV":
//            eloquaEmail.setBusinessWeeks(lowVolumeBusinessWeeks);
//
//            eventId = approvalLowVolumeEventId;
//            eventHash = approvalLowVolumeEventHash;
//
//            break;
          case "Reject":
            eloquaEmail.setInlineProgrammingUrl(inlineProgrammingUrl);
            eloquaEmail.setGangProgrammingUrl(gangProgrammingUrl);
            eloquaEmail.setCustomerSupportUrl(customerSupportUrl);

            eventId = rejectionEventId;
            eventHash = rejectionEventHash;

            break;
        }

        eloquaEmailService.sendEloquaMail(eloquaEmail, eventId, eventHash);
        return "OK";
    }

    return "Ignored";
  }

}