--liquibase formatted sql

--changeset a0215534:001
create table OTP_REQ (
  TX_ID varchar2(40) not null,
  TX_TYPE varchar2(40),
  SRC_APP varchar2(40) not null,
  OPN_ORIG varchar2(40),
  OPN_NEW varchar2(40),
  OPN_NEW_OLD_MATL varchar2(40),
  OTP_CFG clob,
  STATUS varchar2(4000),
  SUCCESS char(1) default '1' not null,
  ERR_MSG varchar2(4000),
  SENT_DATE timestamp,
  CREATED_DATE timestamp default systimestamp not null,
  UUID raw(16) default sys_guid() not null
);
--rollback drop table OTP_REQ;

--changeset a0866112:002
alter table OTP_REQ add constraint OTP_REQ_PK primary key (UUID);
--rollback alter table OTP_REQ drop constraint OTP_REQ_PK;

--changeset a0866112:003
alter table OTP_REQ add constraint OTP_REQ_CC_SUCCESS check (SUCCESS in ('0', '1'));
--rollback alter table OTP_REQ drop constraint OTP_REQ_CC_SUCCESS;

--changeset a0866112:004
create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));
--rollback drop index OTP_REQ_IX_STATUS;

--changeset a0866112:005
create index OTP_REQ_IX_OPN_NEW on OTP_REQ (OPN_NEW, TX_ID, CREATED_DATE);
--rollback drop index OTP_REQ_IX_OPN_NEW;

--changeset a0866112:006
create index OTP_REQ_IX_CFG_LEN on OTP_REQ (dbms_lob.getlength(OTP_CFG), TX_ID, CREATED_DATE);
--rollback drop index OTP_REQ_IX_CFG_LEN;

--changeset a0866112:007
alter table OTP_REQ add (
  OPN_NEW_GPN varchar2(40)
);
--rollback alter table OTP_REQ drop column OPN_NEW_GPN;

--changeset a0866112:008
drop index OTP_REQ_IX_STATUS;
alter table OTP_REQ modify (STATUS varchar2(40));
create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));
--rollback drop index OTP_REQ_IX_STATUS;
--rollback alter table OTP_REQ modify (STATUS varchar2(20));
--rollback create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));

--changeset a0866112:009
drop index OTP_REQ_IX_STATUS;
alter table OTP_REQ modify (STATUS varchar2(4000), ERR_MSG varchar2(4000));
create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));
--rollback drop index OTP_REQ_IX_STATUS;
--rollback alter table OTP_REQ modify (STATUS varchar2(20));
--rollback create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));
