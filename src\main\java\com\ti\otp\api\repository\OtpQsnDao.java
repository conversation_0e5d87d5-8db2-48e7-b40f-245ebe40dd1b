package com.ti.otp.api.repository;

import com.ti.otp.api.domain.OtpQsn;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OtpQsnDao {
  void createQsn(OtpQsn qsn);
  List<String> getFunctionUsers(String fnctName);
  List<OtpQsn> getQsnsByType(String txType);
  List<OtpQsn> getQsnsByTypePagination(String txType, int pageSize, int offset);
  int getQsnsCountByType(String txType);
  OtpQsn getQsnsByTypeId(String txType, String txId);
  int updateQsnPrice(String txType, String txId, String price);
  int updateQsnWwAccountIdAndCompanyName(String txType, String txId, String wwAccountId, String companyName);
  List<OtpQsn> getQsnsWithoutWwAccIdByType(String txType);
  List<OtpQsn> getQsnsWithoutWwAccIdAndRequiringFollowUpByType(String txType, int daysBeforeFollowUp);
  int updateQsnWwAccIdFollowUpDate(String txType, String txId);
}
