package com.ti.otp.api.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@ConfigurationProperties(prefix = "eloqua")
@Getter
@Setter
@Slf4j
public class EloquaEmailConfig {
    private boolean enabled;
    private String basePath;
    private String userId;
    private String password;

    @PostConstruct
    public void run(){
        log.info("Eloqua mailing enabled: {}", enabled);
    }
}
