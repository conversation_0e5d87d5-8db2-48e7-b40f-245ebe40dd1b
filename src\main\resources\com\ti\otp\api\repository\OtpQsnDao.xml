<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ti.otp.api.repository.OtpQsnDao">
    <sql id="queryQsn">
        select QSN.TX_TYPE,
            QSN.TX_ID,
            QSN.CREATED_DATE,
            QSN.QSN_DATA as QSN_DATA_JSON,
            QSN.PRICE,
            QSN.WW_ACCOUNT_ID,
            QSN.OPN_NEW,
            QSN.OPN_NEW_GPN,
            QSN.ORDER_OPN AS OPN_ORIG,
            QSN.ORDER_OTP AS OPN_CFG,
            QSN.USER_EMAIL as EMAIL_TO,
            QSN.USER_FIRST_NAME AS FIRST_NAME,
            QSN.COMPANY_NAME
        from V_OTP_QSN QSN
    </sql>

    <select id="getFunctionUsers" resultType="string">
        select FNCT.USER_ID
        from OTP_QSN_FNCT FNCT
        where FNCT.FNCT_NAME = #{fnctName}
    </select>

    <select id="getQsnsByType" resultType="OtpQsn">
        <include refid="queryQsn" />
        where QSN.TX_TYPE = #{txType}
    </select>

    <select id="getQsnsByTypePagination" resultType="OtpQsn">
        <include refid="queryQsn" />
        where QSN.TX_TYPE = #{txType}
        ORDER BY QSN.CREATED_DATE DESC
        OFFSET #{offset} ROWS
        FETCH FIRST #{pageSize} ROWS ONLY 
    </select>

    <select id="getQsnsCountByType" resultType="int">
        select count(*)
        from OTP_QSN QSN
        where QSN.TX_TYPE = #{txType}
    </select>   

    <select id="getQsnsByTypeId" resultType="OtpQsn">
        <include refid="queryQsn" />
        where QSN.TX_TYPE = #{txType}
            and QSN.TX_ID = #{txId}
    </select>

    <select id="getQsnsWithoutWwAccIdByType" resultType="OtpQsn">
        <include refid="queryQsn" />
        WHERE QSN.TX_TYPE = #{txType}
        AND QSN.WW_ACCOUNT_ID IS NULL
        AND JSON_VALUE(QSN.QSN_DATA, '$.order.questionnaire.orderType') != 'samples'
    </select>

    <select id="getQsnsWithoutWwAccIdAndRequiringFollowUpByType" resultType="OtpQsn">
        <include refid="queryQsn" />
        WHERE QSN.TX_TYPE = #{txType}
        AND QSN.WW_ACCOUNT_ID IS NULL
        AND (QSN.WW_ACC_ID_FOLLOW_UP_DATE IS NULL OR
        QSN.WW_ACC_ID_FOLLOW_UP_DATE &lt;= SYSTIMESTAMP - #{daysBeforeFollowUp})
        AND JSON_VALUE(QSN.QSN_DATA, '$.order.questionnaire.orderType') != 'samples'
    </select>

    <update id="updateQsnWwAccIdFollowUpDate">
        UPDATE OTP_QSN
        SET WW_ACC_ID_FOLLOW_UP_DATE = SYSTIMESTAMP
        WHERE TX_TYPE = #{txType}
        AND TX_ID = #{txId}
    </update>

    <insert id="createQsn">
        insert into OTP_QSN (
            TX_TYPE,
            TX_ID,
            QSN_DATA
        ) values (
            #{txType},
            #{txId},
            #{qsnDataJson}
        )
    </insert>

    <update id="updateQsnPrice">
        update OTP_QSN
        set PRICE = #{price, jdbcType=VARCHAR}
        where TX_TYPE = #{txType}
            and TX_ID = #{txId}
    </update>

    <update id="updateQsnWwAccountIdAndCompanyName">
        update OTP_QSN
        set
            WW_ACCOUNT_ID = #{wwAccountId, jdbcType=VARCHAR},
            COMPANY_NAME = #{companyName, jdbcType=VARCHAR}
        where
            TX_TYPE = #{txType}
            and TX_ID = #{txId}
    </update>
</mapper>
