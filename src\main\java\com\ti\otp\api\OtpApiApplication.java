package com.ti.otp.api;

import com.ti.spring.boot.security.headerconsumer.autoconfigure.EnableTiSecurityHeaderConsumer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@EnableTiSecurityHeaderConsumer
public class OtpApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(OtpApiApplication.class, args);
	}

}
