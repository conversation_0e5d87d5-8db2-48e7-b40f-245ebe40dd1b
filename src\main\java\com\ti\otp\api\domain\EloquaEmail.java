package com.ti.otp.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EloquaEmail {
    private String emailTo;
    private String firstName;
    private String environment;
    private String customPartNumber;
    private String samplePrice;
    private String thresholdWeeks;
    private String thresholdUnits;
    private String buyUrl;
    private String inlineProgrammingUrl;
    private String gangProgrammingUrl;
    private String customerSupportUrl;
    private String businessWeeks;
    private String businessDays;
}
