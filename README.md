# OTP-API
OTP-API is a Java Spring Boot application that provides the following features:
* REST API to submit OTP request to Kafka queue
* REST API to query OTP request/configuration
* Consume OTP request from Kafka queue and write to an app database
* Poll app database for updates and write to Kafka queue
* REST APIs to submit questionnaires for TPLD flow
---
# Getting Started
To run the application from the source repository using Maven:
```
mvn spring-boot:run
```
To run the compiled binary:
```
java -jar otp-api-VERSION.jar
```

---
## Configuration
Configurations are defined using a properties file. Create an `application-default.properties` file in the appropriate directory:
* For source repository: Create the file under `src/main/resources`
* For compiled binary: Create the file in the same folder as the JAR file

Sample `application-default.properties` definitions:
```
spring.datasource.url=******************************************
spring.datasource.username=USERNAME
spring.datasource.password=PASSWORD

app.kafka.enabled=true
app.kafka.bootstrap-servers=lelvrpkafdev03.itg.ti.com:9092
app.kafka.consumer.group-id=otp-chen
app.kafka.topic.inbound=OTP_CHEN_IN
app.kafka.topic.outbound=OTP_CHEN_OUT

app.db-polling.enabled=true
app.db-polling.interval-ms=10000
app.db-polling.source-app=CHEN
app.db-polling.source-status=SUCCESS
```

Available settings in properties file:

Setting | Description                                                              | Default 
------- |--------------------------------------------------------------------------| -------
spring.datasource.url | JDBC URL for the app database                                            |
spring.datasource.username | App database user name                                                   |
spring.datasource.password | App database password                                                    |
app.api.writable | `true` to enable REST API for writing inbound records                    | false
app.kafka.enabled | `true` to enable Kafka integration                                       | false
app.kafka.bootstrap-servers | Kafka server name:port                                                   | lelvrpkafdev03.itg.ti.com:9092
app.kafka.security.protocol | Kafka security protocol                                                  |
app.kafka.sasl.mechanism | Kafka authentication mechanism                                           | 
app.kafka.sasl.jaas.config | Kafka authentication configuration                                       |
app.kafka.consumer.group-id | Kafka consumer group ID                                                  |
app.kafka.filter-type | If specified, process only inbound records from specific request type    |
app.kafka.filter-app | If specified, process only inbound records from specific app name        |
app.kafka.filter-status | If specified, process only inbound records with specific status value    |
app.kafka.filter-success | `true` to process only inbound records with `true` success flag          | true
app.kafka.topic.inbound | Kafka topic name for incoming data (comma-separated if multiple)         |
app.kafka.topic.outbound | Kafka topic name for outgoing data (app1:topic1,app2:topic2,...,default) |
app.kafka.store-inbound | `true` to store inbound records into the database                        | true
app.db-polling.enabled | `true` to enable app database polling for outbound records               | false
app.db-polling.interval-ms | Time interval to poll database (milliseconds)                            | 10000
app.db-polling.source-app | App name filter for outbound records                                     |
app.db-polling.source-status | (Optional) Status filter for outbound records                            |
app.tasksvc.enabled | `true` to enable task service integration                                | false
app.tasksvc.basePath | Apigee server for task service integration                               | https://api-dev.itg.ti.com
app.tasksvc.tokenUrl | Apigee server path for OAuth                                             | /v1/oauth/
app.tasksvc.tokenClientId | Apigee OAuth client ID                                                   |
app.tasksvc.tokenClientSecret | Apigee OAuth client secret                                               |
app.tasksvc.userId | Task service API user (authorized to create tasks)                       |
app.ww-acc-id.refresh.cron.expression | Expression for enabling cron refresh                                     |
app.ww-acc-id.refresh.api.enabled | `true` to enable cron refresh                                            | false
app.ww-acc-id.refresh.api.url | URL for WW Account ID refresh                                            | https://dmboomi-sit.itg.ti.com:9093/ws/rest/myti/mytidata
app.ww-acc-id.refresh.api.username | Username for accessing API                                               
app.ww-acc-id.refresh.api.password | Password for API authentication                                          
app.new-cust.setup.email.enabled | `true` to enable follow up email to new customers                        | false
app.new-cust.setup.email.from | From email id for sending follow up emails to new customers              | <EMAIL>
app.new-cust.setup.email.to | To email id for sending follow up emails to new customers                
app.new-cust.setup.email.subject | Email subject for sending follow up emails to new customers              | New customer setup required for TPLD [LOCAL]
app.new-cust.setup.email.template | Email template for sending follow up emails to new customers                                                          | new_cust_setup_email_template.html
app.new-cust.setup.email.days-before-follow-up | Frequency of sending follow up mails                                     | 2
Sample Kafka authentication settings:
```
app.kafka.security.protocol=SASL_SSL
app.kafka.sasl.mechanism=SCRAM-SHA-512
app.kafka.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="MY_USERNAME" password="MY_PASSWORD";
```

---
## App Database Schema
Manually create the required tables/views in the app database by running the SQL script provided at `src/main/resources/schema/table.sql`. This will create the `OTP_REQ` table.

The `OTP_REQ` table contains records received from Kafka queue and records to be sent to Kafka queue.

Table columns:

Column | Description | Comment
------ | ----------- | -------
UUID | UUID of record | System generated
TX_ID | Request ID string | Defined by the initial system creating the record
TX_TYPE | Request type string | Defined by the initial system creating the record
TX_DATA | Transaction data message | includes wwAccountId, customer email, price and OTP config string
TX_ACTION | Request type string | Action to be performed by the current app
SRC_APP | Name of app that published the record |
OPN_ORIG | Original orderable part number |
OPN_NEW | New orderable part number | May be `null` in the initial record
OPN_NEW_OLD_MATL | New orderable part number's old material name | May be `null` in the initial record
STATUS | Status message of the record |
SUCCESS | Success flag for the record | "0" or "1" (default)
ERR_MSG | Error message | May be `null` if there are no errors
CREATED_DATE | Date when record was received | System generated
SENT_DATE | Date when record was sent to Kafka | System generated

## Task Service Integration Database Schema (Optional)
Manually create the required table in the app database by running the SQL script provided at `src/main/resources/schema/table-tasksvc.sql`. This will create the `OTP_TASKSVC_CFG` table.

The `OTP_TASKSVC_CFG` table contains configurations for task service integration.

Table columns:

Column | Description | Comment
------ | ----------- | -------
UUID | UUID of record | System generated
SRC_APP | Name of app for the inbound record |
APP_UUID | UUID of app registered in Task Service
TMPL_UUID | UUID of template registered in Task Service
TGT_APP | Name of app for the outbound record |
SUCCESS_STATE | State name when task is considered successful |
FAILURE_STATE | State name when task is considered failed |
