package com.ti.otp.api.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@Getter
@Setter
@Slf4j
public abstract class GenericEmailConfig {

    private boolean enabled;
    private String from;
    private String tos;
    private String subject;
    private int daysBeforeFollowUp;

    @PostConstruct
    public void run(){
        log.info("New Customer Setup Email enabled: {}", enabled);
        if(enabled){
            log.debug("New Customer Setup Email days before follow up: {}", daysBeforeFollowUp);
        }
    }
}
