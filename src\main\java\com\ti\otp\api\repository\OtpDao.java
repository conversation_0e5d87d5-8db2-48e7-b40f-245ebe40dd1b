package com.ti.otp.api.repository;

import com.ti.otp.api.domain.OtpReq;
import com.ti.otp.api.domain.OtpTaskSvcCfg;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OtpDao {
  void createReq(OtpReq req);
  String generateUuid();
  OtpReq getLatestReqByTxId(String txId);
  OtpReq getReqByTxIdApp(String txId, String srcApp);
  OtpReq getReqByUuid(String uuid);
  List<OtpReq> getReqsByTxId(String txId);
  List<OtpReq> getUnsentReqsByAppStatus(String srcApp, String status);
  String getOtpCfgByOpnNew(String opnNew);
  String getOtpCfgByOpnNewFromQsn(String opnNew);
  OtpTaskSvcCfg getTaskSvcCfgBySrcApp(String srcApp);
  OtpTaskSvcCfg getTaskSvcCfgByTmplUuid(String tmplUuid);
  void markReqAsSent(String uuid);
}
