package com.ti.otp.api.kafka;

import com.ti.otp.api.config.ListenFailureEmailConfig;
import com.ti.otp.api.domain.EloquaErrorException;
import com.ti.otp.api.domain.InvalidTxIdException;
import com.ti.otp.api.service.EmailService;
import com.ti.otp.api.service.ErtpOtpService;
import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import javax.annotation.PostConstruct;
import javax.mail.MessagingException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import com.ti.otp.api.config.TaskSvcConfig;
import com.ti.otp.api.domain.OtpReq;
import com.ti.otp.api.service.OtpService;
import com.ti.otp.api.service.TaskSvcService;

@Component
@Slf4j
public class KafkaMessageConsumer {

  @Autowired
  TaskSvcConfig taskSvcConfig;

  @Autowired
  TaskSvcService taskSvcService;

  @Value(value = "${app.kafka.enabled:false}")
  private boolean enabled;

  @Value(value = "${app.kafka.store-inbound}")
  private boolean storeInbound;

  @Value(value = "#{'${app.kafka.topic.inbound}'.split(',')}")
  private String[] inboundTopics;

  @Value(value = "${app.kafka.filter-type}")
  private String filterType;

  @Value(value = "${app.kafka.filter-app}")
  private String filterApp;

  @Value(value = "${app.kafka.filter-status}")
  private String filterStatus;

  @Value(value = "${app.kafka.filter-success}")
  private String filterSuccess;

  @Autowired
  private EmailService emailService;

  @Autowired
  private ListenFailureEmailConfig listenFailureEmailConfig;

  private boolean ignoreType = false;
  private boolean ignoreApp = false;
  private boolean ignoreStatus = false;
  private boolean ignoreSuccess = false;

  @Autowired
  private OtpService otpService;

  @Autowired
  private ErtpOtpService ertpOtpService;

  @PostConstruct
  public void init() {
    log.info("Kafka consumer enabled = {}", enabled);
    if (enabled) {
      if (inboundTopics == null) {
        inboundTopics = new String[0];
      }
      log.info("Inbound topics = {}", Arrays.asList(inboundTopics));
      if (inboundTopics.length > 0) {
        if (filterType == null || filterType.isEmpty() || filterType.equals("*")) {
          ignoreType = true;
        }
        if (filterApp == null || filterApp.isEmpty() || filterApp.equals("*")) {
          ignoreApp = true;
        }
        if (filterStatus == null || filterStatus.isEmpty() || filterStatus.equals("*")) {
          ignoreStatus = true;
        }
        if (filterSuccess == null || filterSuccess.isEmpty() || filterSuccess.equalsIgnoreCase("false")) {
          ignoreSuccess = true;
        }

        log.info("Inbound filter: Type = '{}'", filterType);
        log.info("Inbound filter: App name = '{}'", filterApp);
        log.info("Inbound filter: Status name = '{}'", filterStatus);
        log.info("Inbound filter: Success flag = '{}'", filterSuccess);
      }
    }
  }

  @KafkaListener(
          topics = "#{'${app.kafka.topic.inbound}'.split(',')}",
          groupId = "${app.kafka.consumer.group-id:otp-default}",
          properties = {"spring.json.value.default.type=com.ti.otp.api.domain.OtpReq"},
          autoStartup = "${app.kafka.enabled:false}")
  public void processErtpOtpReqForTPLD(@Payload OtpReq req) throws MessagingException {
    try {
      log.info("Received Kafka request: {}", req);
      boolean matchType = ignoreType || filterType.equalsIgnoreCase(req.getTxType());
      boolean matchApp = ignoreApp || filterApp.equalsIgnoreCase(req.getSrcApp());
      boolean matchStatus = ignoreStatus || filterStatus.equalsIgnoreCase(req.getStatus());
      boolean matchSuccess = ignoreSuccess || req.isSuccess();
      if (matchType && matchApp && matchStatus && matchSuccess) {
        ertpOtpService.processRequest(req);
      }
    }

    catch (Exception e) {

      String msg = "Encountered the following error: "+e.toString()+"\nWhile processing the Kafka request: "+req;
      log.error(msg);

      emailService.send(msg, listenFailureEmailConfig);
    }
  }
}