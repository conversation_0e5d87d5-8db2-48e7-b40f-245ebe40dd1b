package com.ti.otp.api.controller;

import com.ti.otp.api.domain.KafkaErrorException;
import com.ti.otp.api.domain.OtpReq;
import com.ti.otp.api.domain.UnknownException;
import com.ti.otp.api.kafka.KafkaMessageConsumer;
import com.ti.otp.api.kafka.KafkaMessageProducer;
import com.ti.otp.api.service.OtpService;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.mail.MessagingException;

@RestController
public class OtpController {
  @Autowired
  private OtpService otpService;

  @Autowired
  private KafkaMessageConsumer kafkaMessageConsumer;

  @Autowired
  private KafkaMessageProducer kafkaMessageProducer;

  @Value(value = "${app.api.writable}")
  private boolean writable;

  @Hidden
  @PostMapping("/requests/send")
  public ResponseEntity<String> sendRequest(@RequestBody OtpReq req) throws MessagingException, UnknownException, KafkaErrorException {
    if (!writable) {
      return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).body("Write API not enabled");
    }
    if (!kafkaMessageProducer.isEnabled()) {
      return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).body("Kafka integration not enabled");
    }
    String topicOut = kafkaMessageProducer.sendReq(req);
    return ResponseEntity.accepted().body(topicOut);
  }

  @Operation(summary = "Get the latest message for a request")
  @ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Latest message for a request"),
    @ApiResponse(responseCode = "404", description = "Request not found", content = @Content),
  })
  @GetMapping("/requests/{txId}/latest")
  public ResponseEntity<OtpReq> getLatestRequestByTxId(@Parameter(description = "Request ID") @PathVariable("txId") String txId) {
    OtpReq req = otpService.getLatestReqByTxId(txId);
    if (req == null) {
      return ResponseEntity.notFound().build();
    } else {
      return ResponseEntity.ok(req);
    }
  }

  @Operation(summary = "Get all messages for a request")
  @ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "List of messages for a request"),
    @ApiResponse(responseCode = "404", description = "Request not found", content = @Content),
  })
  @GetMapping("/requests/{txId}")
  public ResponseEntity<List<OtpReq>> getRequestsByTxId(@Parameter(description = "Request ID") @PathVariable("txId") String txId) {
    List<OtpReq> req = otpService.getReqsByTxId(txId);
    if (req == null || req.isEmpty()) {
      return ResponseEntity.notFound().build();
    } else {
      return ResponseEntity.ok(req);
    }
  }

  @Hidden
  @GetMapping("/src-apps")
  public ResponseEntity<List<String>> getSourceApps() {
    ArrayList<String> srcApps = new ArrayList<>(kafkaMessageProducer.getSrcApps());
    return ResponseEntity.ok(srcApps);
  }

  @Hidden
  @Operation(summary = "Get OTP config string for an OPN")
  @ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "OTP config string for an OPN",
      content = {@Content(mediaType = MediaType.TEXT_PLAIN_VALUE,
        schema = @Schema(description = "OTP config string", example = ":100000000fbc1f2100d1a6a1920220b81fda6f6e8b"))}),
    @ApiResponse(responseCode = "404", description = "Request not found", content = @Content),
  })
  @GetMapping("/otp-cfgs/{opnNew}")
  public ResponseEntity<String> getOtpCfgByOpnNew(@PathVariable("opnNew") String opnNew) {
    String otpCfg = otpService.getOtpCfgByOpnNew(opnNew);
    if (otpCfg == null || otpCfg.isEmpty()) {
      return ResponseEntity.notFound().build();
    } else {
      return ResponseEntity.ok(otpCfg);
    }
  }

  @Operation(summary = "Get OTP config string for an OPN by Transaction type")
  @ApiResponses(value = {
          @ApiResponse(responseCode = "200", description = "OTP config string for an OPN",
                  content = {@Content(mediaType = MediaType.TEXT_PLAIN_VALUE,
                          schema = @Schema(description = "OTP config string", example = ":100000000fbc1f2100d1a6a1920220b81fda6f6e8b"))}),
          @ApiResponse(responseCode = "404", description = "Request not found", content = @Content),
  })
  @GetMapping("/otp-cfgs/{txType}/{opnNew}")
  public ResponseEntity<String> getOtpCfgByTxTypeAndOpnNew(@PathVariable("txType") String txType, @PathVariable("opnNew") String opnNew){
    String otpCfg = otpService.getOtpCfgByTxTypeAndOpnNew(txType, opnNew);
    if(otpCfg == null || otpCfg.isEmpty()){
      return ResponseEntity.notFound().build();
    }
    return ResponseEntity.ok(otpCfg);
  }

  @Hidden
  @GetMapping("/uuids/generate")
  public ResponseEntity<String> generateUuid() {
    String uuid = otpService.generateUuid();
    return ResponseEntity.ok(uuid);
  }
}
