package com.ti.otp.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OtpQsn {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private String txType;
    private String txId;
    private Date createdDate;
    private JsonNode qsnData;
    private String price;
    private String wwAccountId;
    private String opnNew;
    private String opnNewGpn;
    private String opnOrig;
    private String opnCfg;
    private String emailTo;
    private String firstName;
    private String companyName;

    @JsonIgnore
    private String qsnDataJson;

    public void setQsnData(JsonNode qsnData) {
        this.qsnData = qsnData;
        if (qsnData != null) {
            try {
                this.qsnDataJson = OBJECT_MAPPER.writeValueAsString(qsnData);
            } catch (Exception e) {
                this.qsnDataJson = "{}";
            }
        }
    }

    public void setQsnDataJson(String qsnDataJson) {
        this.qsnDataJson = qsnDataJson;
        if (qsnDataJson != null) {
            try {
                this.qsnData = OBJECT_MAPPER.readTree(qsnDataJson);
            } catch (Exception e) {
                this.qsnData = null;
            }
        }
    }

}
