--liquibase formatted sql

/*=====================================
 * Change column sizes
 *
 */
--changeset a0215534:001
drop index OTP_REQ_IX_STATUS;
--rollback create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));;

--changeset a0215534:002
alter table OTP_REQ modify (STATUS varchar2(4000), ERR_MSG varchar2(4000));
--rollback alter table OTP_REQ modify (STATUS varchar2(20), ERR_MSG varchar2(100));

--changeset a0215534:003
create index OTP_REQ_IX_STATUS on OTP_REQ (STATUS, SRC_APP, SENT_DATE, rawtohex(UUID));
--rollback drop index OTP_REQ_IX_STATUS;

/*=====================================
 * Update Views
 *
 */
--changeset a0215534:004 runAlways:true runOnChange:true
create or replace view V_OTP_REQ_LATEST as
select *
from (
    select REQ.*,
        row_number() over (partition by TX_ID order by CREATED_DATE desc) as RN
    from OTP_REQ REQ
)
where RN = 1;
--rollback drop view V_OTP_REQ_LATEST;

--changeset a0215534:005 runAlways:true runOnChange:true
create or replace view V_OTP_REQ_LATEST_APP as
select *
from (
    select REQ.*,
        row_number() over (partition by TX_ID, SRC_APP order by CREATED_DATE desc) as RN
    from OTP_REQ REQ
)
where RN = 1;
--rollback drop view V_OTP_REQ_LATEST_APP;

--changeset a0215534:006 runAlways:true runOnChange:true
create or replace view V_OTP_OPN_CFG as
select ONT.OPN_NEW, ONT.TX_ID, TOC.OTP_CFG from (
  select OPN_NEW, TX_ID from (
    select OPN_NEW, TX_ID, row_number() over (partition by OPN_NEW order by CREATED_DATE) as RN
    from OTP_REQ
    where OPN_NEW is not null
  ) where RN = 1
) ONT left join (
  select TX_ID, OTP_CFG from (
    select TX_ID, OTP_CFG, row_number() over (partition by TX_ID order by CREATED_DATE) as RN
    from OTP_REQ
    where dbms_lob.getlength(OTP_CFG) > 0
  ) where RN = 1
) TOC on ONT.TX_ID = TOC.TX_ID;
--rollback drop view V_OTP_OPN_CFG;
