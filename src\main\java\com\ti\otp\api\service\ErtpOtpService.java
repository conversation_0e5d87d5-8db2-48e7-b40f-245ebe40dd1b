package com.ti.otp.api.service;

import com.ti.otp.api.config.EloquaEmailConfig;
import com.ti.otp.api.config.ListenFailureEmailConfig;
import com.ti.otp.api.domain.*;
import com.ti.otp.api.repository.OtpQsnDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.Objects;

@Service
@Slf4j
public class ErtpOtpService {

    @Autowired
    private OtpQsnDao otpQsnDao;

    @Autowired
    private EloquaEmailConfig eloquaEmailConfig;

    @Autowired
    private EloquaEmailService eloquaEmailService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private ListenFailureEmailConfig listenFailureEmailConfig;

    @Value(value = "${eloqua.completion-high-volume-event-id}")
    private String completionHighVolumeEventId;

    @Value(value = "${eloqua.completion-high-volume-event-hash}")
    private String completionHighVolumeEventHash;

    @Value(value = "${eloqua.environment}")
    private String environment;

    @Value(value = "${eloqua.high-volume-threshold-weeks}")
    private String highVolumeThresholdWeeks;

    @Value(value = "${eloqua.high-volume-threshold-units}")
    private String highVolumeThresholdUnits;

    @Value(value = "${eloqua.buyUrl}")
    private String buyUrl;

    @Value(value = "${eloqua.inline-programming-url}")
    private String inlineProgrammingUrl;

    @Value(value = "${eloqua.gang-programming-url}")
    private String gangProgrammingUrl;

    @Value(value = "${eloqua.customer-support-url}")
    private String customerSupportUrl;

    public void processRequest(OtpReq otpReq) throws MessagingException, DbErrorException {

        OtpQsn otpQsn = null;

        try {
            otpQsn = otpQsnDao.getQsnsByTypeId("TPLD_CUSTOM_OPN", otpReq.getTxId());
        }

        catch (Exception e) {
            String errMsg = e.getMessage();
            String logMsg = "Encountered the following DB connection error: " + errMsg + ", while processing the request: " + otpReq;

            log.error(logMsg);
            emailService.send(logMsg, listenFailureEmailConfig);
        }

        if (Objects.isNull(otpQsn)) {
            String errMsg = "Invalid Tx Id. Didn't find required fields from questionnaire for the given Tx Id";
            String logMsg = "Encountered the following error: "+ errMsg + ", while processing the request: " + otpReq;

            log.error(errMsg);
            emailService.send(logMsg, listenFailureEmailConfig);
        }

        EloquaEmail eloquaEmail = new EloquaEmail();

        eloquaEmail.setEmailTo(otpQsn.getEmailTo());
        eloquaEmail.setFirstName(otpQsn.getFirstName());
        eloquaEmail.setEnvironment(environment);
        eloquaEmail.setCustomPartNumber(otpQsn.getOpnNew());
        eloquaEmail.setSamplePrice(otpQsn.getPrice());
        eloquaEmail.setThresholdWeeks(highVolumeThresholdWeeks);
        eloquaEmail.setThresholdUnits(highVolumeThresholdUnits);
        eloquaEmail.setBuyUrl(buyUrl);
        eloquaEmail.setInlineProgrammingUrl(inlineProgrammingUrl);
        eloquaEmail.setGangProgrammingUrl(gangProgrammingUrl);
        eloquaEmail.setCustomerSupportUrl(customerSupportUrl);

        try {
            eloquaEmailService.sendEloquaMail(eloquaEmail, completionHighVolumeEventId, completionHighVolumeEventHash);
        }

        catch (EloquaErrorException e) {
            //already handled
            return;
        }
    }
}