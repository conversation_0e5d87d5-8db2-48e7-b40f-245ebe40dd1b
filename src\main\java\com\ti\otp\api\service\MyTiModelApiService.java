package com.ti.otp.api.service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.ti.otp.api.config.MyTiModelApiConfig;
import com.ti.otp.api.domain.mytimodel.CompanyProfile;
import com.ti.otp.api.domain.mytimodel.UserCompanyAssoc;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class MyTiModelApiService {
  private final static String API_USER_COMPANIES_PATH = "/users/companies";
  private final static String API_COMPANIES_PATH = "/companies";

  public static class ExceptionMessages {
    public static final String DEFAULT = "An error occurred when connecting to the mytimodel api";
  }

  private final MyTiModelApiConfig apiConfig;
  private final RestTemplate restTemplate = new RestTemplate();

  private HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    return headers;
  }

  private <T, U> ResponseEntity<T> callApi(@NotBlank String url, @NotNull HttpMethod method,
      @Nullable U data, ParameterizedTypeReference<T> classType) {
    try {
      HttpEntity<U> entity = new HttpEntity<>(data, generateHeaders());
      log.debug("Sending request to mytimodel api: {}", url);
      log.debug("with http body: {}", entity.getBody());
      return restTemplate.exchange(url, method, entity, classType);
    } catch (RestClientException e) {
      log.error(ExceptionMessages.DEFAULT, e);
      throw new RuntimeException(ExceptionMessages.DEFAULT);
    }
  }

  public boolean isEnabled() {
    return apiConfig.isEnabled();
  }

  public List<UserCompanyAssoc> getUserCompanyAssociationsByUserId(@NotBlank String userId) {
    if (!isEnabled()) {
      return null;
    }
    ResponseEntity<List<UserCompanyAssoc>> response = callApi(
        apiConfig.getBaseUrl() + API_USER_COMPANIES_PATH + "?userId=" + userId,
        HttpMethod.GET,
        HttpEntity.EMPTY,
        new ParameterizedTypeReference<List<UserCompanyAssoc>>() {});
    return response.getBody();
  }

  public CompanyProfile getCompanyProfileByCompanyId(@NotNull Long id) {
    if (!isEnabled()) {
      return null;
    }
    ResponseEntity<CompanyProfile> response = callApi(
        apiConfig.getBaseUrl() + API_COMPANIES_PATH + "/" + id.toString(),
        HttpMethod.GET,
        HttpEntity.EMPTY,
        new ParameterizedTypeReference<CompanyProfile>() {});
    return response.getBody();
  }

  public String getCompanyWwAccountIdByUserId(@NotBlank String userId) {
    if (!isEnabled()) {
      return null;
    }
    List<UserCompanyAssoc> userCompanyAssocs = getUserCompanyAssociationsByUserId(userId);
    if (userCompanyAssocs == null || userCompanyAssocs.isEmpty()) {
      log.debug("No user company associations found (userId='{}')", userId);
      return null;
    }
    List<String> wwAccountIds = userCompanyAssocs.stream()
        .map(uca -> {
          String wwAccountId = null;
          Long cpId = uca.getCpId();
          if (cpId == null) {
            log.error("User company association: cpId is null");
            return wwAccountId;
          }
          CompanyProfile companyProfile = getCompanyProfileByCompanyId(cpId);
          if (companyProfile == null) {
            log.error("User company association: company profile not found (cpId='{}')", cpId);
            return wwAccountId;
          }
          return companyProfile.getWwAccountId();
        })
        .filter(id -> id != null)
        .distinct()
        .sorted()
        .collect(Collectors.toList());
    if (wwAccountIds.isEmpty()) {
      log.debug("WW account id not found (userId='{}')", userId);
      return null;
    }
    if (wwAccountIds.size() > 1) {
      log.warn("Multiple ww account ids found; using first (userId='{}')", userId);
    }
    log.info("WW account id found (userId='{}', wwAccountId='{}')", userId, wwAccountIds.get(0));
    return wwAccountIds.get(0);
  }
}
