package com.ti.otp.api.controller;

import com.ti.otp.api.domain.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ti.otp.api.service.TaskSvcService;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;

import javax.mail.MessagingException;

@RestController
@Slf4j
public class TaskSvcController {

  @Autowired
  TaskSvcService taskSvcService;

  @Hidden
  @PostMapping("/tasksvc-pricing-callback")
  public ResponseEntity<String> processCallback(@RequestBody TaskSvcTriggerRequest triggerReq) throws UnknownException, KafkaErrorException, MessagingException {
    String taskUuid = triggerReq.getTaskUuid();
    String stateName = triggerReq.getStateName();
    log.debug("Received TaskSvc trigger callback: {} = {}", taskUuid, stateName);
    boolean result = taskSvcService.processCallback(triggerReq);
    log.debug("TaskSvc trigger callback result: {} = {} = {}", taskUuid, stateName, result);
    return ResponseEntity.ok(String.valueOf(result));
  }

  @Hidden
  @PostMapping("/tasksvc-callback")
  public ResponseEntity<String> triggerCallback(@RequestBody TaskSvcTriggerRequest triggerReq) throws UnknownException, KafkaErrorException, MessagingException, EloquaErrorException, InvalidTxIdException, DbErrorException {

    String branchName = triggerReq.getBranchName();
    log.info("Received TaskSvc trigger branch callback, with branch name: {}", branchName);

    String result = taskSvcService.triggerCallback(triggerReq);
    return ResponseEntity.ok(result);
  }
}
