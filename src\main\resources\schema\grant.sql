--liquibase formatted sql

--changeset a0866112:001 runAlways:true runOnChange:true endDelimiter:~
declare
    STMT varchar2(500);
begin
    for c1 in (
        select * from (
            select USERTYPE, USERNAME, OBJECT_TYPE, OBJECT_NAME,
                case when OBJECT_TYPE in ('MATERIALIZED VIEW','VIEW') then 'select'
                    when OBJECT_TYPE in ('SEQUENCE') then decode(USERTYPE, 'R', null, 'select')
                    when OBJECT_TYPE = 'TABLE' then decode(USERTYPE, 'R', 'select', 'select,insert,update,delete')
                    when OBJECT_TYPE in ('PACKAGE','FUNCTION','PROCEDURE') then decode(USERTYPE, 'R', null, 'execute')
                end as PRIVS
            from (
                select OBJECT_TYPE, OBJECT_NAME from USER_OBJECTS
                where OBJECT_TYPE in ('FUNCTION','MATERIALIZED VIEW','PACKAGE','PROCEDURE','SEQUENCE','TABLE','VIEW')
                    and not regexp_like(OBJECT_NAME, '^(DATABASECHANGELOG|BATCH_|QUEST_|STG_TEST_|TEST_|T_|TEMP_).*$')
                minus
                select 'TABLE' as OBJECT_TYPE, OBJECT_NAME
                from USER_OBJECTS where OBJECT_TYPE = 'MATERIALIZED VIEW'
            ) OBJ
            cross join (
                select USERNAME, case when USERNAME like 'RO%' then 'R' else 'RW' end as USERTYPE
                from DBA_USERS where regexp_like(USERNAME, '^(TPLDAPP.*|RO_ESBOOMI)$')
            ) USR
        )
        where PRIVS is not null
        order by USERNAME, OBJECT_TYPE, OBJECT_NAME
    ) loop
        STMT := 'grant ' || c1.PRIVS || ' on ' || c1.OBJECT_NAME || ' to ' || c1.USERNAME;
        execute immediate STMT;
    end loop;
end;
~
--rollback select 'N/A' from dual;
