<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ti.otp.api.repository.OtpDao">
    <sql id="selectReq">
        select rawtohex(REQ.UUID) as UUID,
            REQ.TX_ID,
            REQ.TX_TYPE,
            REQ.SRC_APP,
            REQ.OPN_ORIG,
            REQ.OPN_NEW,
            REQ.OPN_NEW_OLD_MATL,
            REQ.OPN_NEW_GPN,
            REQ.TX_DATA,
            REQ.STATUS,
            REQ.SUCCESS,
            REQ.ERR_MSG,
            REQ.SENT_DATE,
            REQ.CREATED_DATE,
            REQ.TX_ACTION
    </sql>

    <sql id="selectTaskSvcCfg">
        select rawtohex(CFG.UUID) as UUID,
            CFG.SRC_APP,
            rawtohex(CFG.APP_UUID) as APP_UUID,
            rawtohex(CFG.TMPL_UUID) as TMPL_UUID,
            CFG.TGT_APP,
            CFG.SUCCESS_STATE,
            CFG.FAILURE_STATE,
            CFG.INIT_BRANCH,
            CFG.INBOUND_FILTER_SUCCESS
    </sql>

    <sql id="queryReq">
        <include refid="selectReq" />
        from OTP_REQ REQ
    </sql>

    <sql id="queryReqLatest">
        <include refid="selectReq" />
        from V_OTP_REQ_LATEST REQ
    </sql>

    <sql id="queryReqLatestApp">
        <include refid="selectReq" />
        from V_OTP_REQ_LATEST_APP REQ
    </sql>

    <sql id="queryTaskSvcCfg">
        <include refid="selectTaskSvcCfg" />
        from OTP_TASKSVC_CFG CFG
    </sql>

    <select id="generateUuid" resultType="string">
        select rawtohex(sys_guid()) from dual
    </select>

    <select id="getLatestReqByTxId" resultType="OtpReq">
        <include refid="queryReqLatest" />
        where REQ.TX_ID = #{txId}
    </select>

    <select id="getReqsByTxId" resultType="OtpReq">
        <include refid="queryReq" />
        where REQ.TX_ID = #{txId}
        order by REQ.CREATED_DATE
    </select>

    <select id="getReqByTxIdApp" resultType="OtpReq">
        <include refid="queryReqLatestApp" />
        where REQ.TX_ID = #{txId}
            and REQ.SRC_APP = #{srcApp}
    </select>

    <select id="getReqByUuid" resultType="OtpReq">
        <include refid="queryReq" />
        where REQ.UUID = hextoraw(#{uuid})
    </select>

    <select id="getUnsentReqsByAppStatus" resultType="OtpReq">
        <include refid="queryReq" />
        where REQ.SRC_APP = #{srcApp}
            and REQ.STATUS = nvl(#{status, jdbcType=VARCHAR}, REQ.STATUS)
            and REQ.SENT_DATE is null
        order by REQ.CREATED_DATE
    </select>

    <select id="getOtpCfgByOpnNew" resultType="string">
        select CFG.TX_DATA
        from V_OTP_OPN_CFG CFG
        where CFG.OPN_NEW = #{opnNew}
    </select>

    <select id="getOtpCfgByOpnNewFromQsn" resultType="string">
        SELECT order_otp FROM v_otp_qsn
        WHERE opn_new = #{opnNew}
    </select>

    <select id="getTaskSvcCfgBySrcApp" resultType="OtpTaskSvcCfg">
        <include refid="queryTaskSvcCfg" />
        where CFG.SRC_APP = #{srcApp}
    </select>

    <select id="getTaskSvcCfgByTmplUuid" resultType="OtpTaskSvcCfg">
        <include refid="queryTaskSvcCfg" />
        where CFG.TMPL_UUID = hextoraw(#{tmplUuid})
    </select>

    <update id="markReqAsSent">
        update OTP_REQ
        set SENT_DATE = sysdate
        where UUID = hextoraw(#{uuid})
            and SENT_DATE is null
    </update>

    <insert id="createReq" useGeneratedKeys="true" keyProperty="uuid" keyColumn="UUID">
        insert into OTP_REQ (
            UUID,
            TX_ID,
            TX_TYPE,
            SRC_APP,
            OPN_ORIG,
            OPN_NEW,
            OPN_NEW_OLD_MATL,
            OPN_NEW_GPN,
            TX_DATA,
            STATUS,
            SUCCESS,
            ERR_MSG,
            TX_ACTION
        ) values (
            nvl(hextoraw(#{uuid, jdbcType=VARCHAR}), sys_guid()),
            #{txId, jdbcType=VARCHAR},
            #{txType, jdbcType=VARCHAR},
            #{srcApp, jdbcType=VARCHAR},
            #{opnOrig, jdbcType=VARCHAR},
            #{opnNew, jdbcType=VARCHAR},
            #{opnNewOldMatl, jdbcType=VARCHAR},
            #{opnNewGpn, jdbcType=VARCHAR},
            #{txData, jdbcType=VARCHAR},
            #{status, jdbcType=VARCHAR},
            #{success, jdbcType=VARCHAR},
            #{errMsg, jdbcType=VARCHAR},
            #{txAction, jdbcType=VARCHAR}
        )
    </insert>
</mapper>
