package com.ti.otp.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskSvcTriggerRequest {
  private String taskUuid;
  private String taskName;
  private String ctxIdLabel;
  private String ctxIdValue;
  private int iteration;
  private String eventType;
  private String eventUserId;
  private String eventDttm;
  private String stateName;
  private String branchName;
}
