--liquibase formatted sql

--changeset a0866112:001 runAlways:true runOnChange:true
create or replace view V_OTP_REQ_LATEST as
select *
from (
    select REQ.*,
        row_number() over (partition by TX_ID order by CREATED_DATE desc) as RN
    from OTP_REQ REQ
)
where RN = 1;
--rollback drop view V_OTP_REQ_LATEST;

--changeset a0866112:002 runAlways:true runOnChange:true
create or replace view V_OTP_REQ_LATEST_APP as
select *
from (
    select REQ.*,
        row_number() over (partition by TX_ID, SRC_APP order by CREATED_DATE desc) as RN
    from OTP_REQ REQ
)
where RN = 1;
--rollback drop view V_OTP_REQ_LATEST_APP;

--changeset a0866112:003 runAlways:true runOnChange:true
create or replace view V_OTP_OPN_CFG as
select ONT.OPN_NEW, ONT.TX_ID, TOC.OTP_CFG from (
  select OPN_NEW, TX_ID from (
    select OPN_NEW, TX_ID, row_number() over (partition by OPN_NEW order by CREATED_DATE) as RN
    from OTP_REQ
    where OPN_NEW is not null
  ) where RN = 1
) ONT left join (
  select TX_ID, OTP_CFG from (
    select TX_ID, OTP_CFG, row_number() over (partition by TX_ID order by CREATED_DATE) as RN
    from OTP_REQ
    where dbms_lob.getlength(OTP_CFG) > 0
  ) where RN = 1
) TOC on ONT.TX_ID = TOC.TX_ID;
--rollback drop view V_OTP_OPN_CFG;
