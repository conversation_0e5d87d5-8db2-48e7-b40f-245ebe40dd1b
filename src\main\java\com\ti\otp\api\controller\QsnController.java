package com.ti.otp.api.controller;

import java.security.Principal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.ti.otp.api.domain.EloquaErrorException;
import com.ti.otp.api.domain.TaskSvcTriggerResponse;
import com.ti.otp.api.service.WwAccIdLookupApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ti.otp.api.domain.OtpQsn;
import com.ti.otp.api.domain.UserContext;
import com.ti.otp.api.service.OtpQsnService;
import com.ti.otp.api.service.TaskSvcService;
import com.ti.tasksvc.client.model.TaskBase;

import javax.mail.MessagingException;

@RestController
public class QsnController {
    @Autowired
    private OtpQsnService otpQsnService;

    @Autowired
    private TaskSvcService taskSvcService;

    @Value(value = "${app.api.writable:false}")
    boolean writable;

    private static final int VALID_WW_ACCT_ID_LENGTH = 10;
    private static final String DIGIT_REGEX = "[0-9]+";
    private static final String WW_ACC_ID_INVALID_LENGTH_BODY = "WW Account ID has to be exactly 10 characters long!";
    private static final String WW_ACCT_ID_INVALID_BODY_SUFFIX = " is an invalid WW Account ID!";
    private static final String WW_ACC_ID_EDIT_CONFLICT_BODY = "WW Account ID cannot be changed post task creation for questionnaire!";

    private String prepareErrorBody(String message){
        return "{\"message\": \"" + message + "\"}";
    }

    @PostMapping("/echo")
    public ResponseEntity<OtpQsn> echo(@RequestBody OtpQsn qsn) {
        return ResponseEntity.ok(qsn);
    }

    @PostMapping("/questionnaires")
    public ResponseEntity<OtpQsn> createQsn(@RequestBody OtpQsn qsn) throws MessagingException, EloquaErrorException {
        if (!writable) {
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
        }
        String txType = qsn.getTxType();
        String txId = qsn.getTxId();
        OtpQsn result = otpQsnService.getQsnsByTypeId(txType, txId);
        if (result != null) {
        return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
        }
        otpQsnService.createQsn(qsn);
        txType = qsn.getTxType();
        txId = qsn.getTxId();
        result = otpQsnService.getQsnsByTypeId(txType, txId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/questionnaires/{txType}")
    public ResponseEntity<Map<String, Object>> getQsnsByType(
            @PathVariable("txType") String txType,
            @RequestParam(value = "pageSize") Integer pageSize,
            @RequestParam(value = "offset") Integer offset) {
        List<OtpQsn> result;
        int totalCount;

        if (pageSize > 0 && offset >= 0) {
          result = otpQsnService.getQsnsByTypePagination(txType, pageSize, offset);
          totalCount = otpQsnService.getQsnsCountByType(txType);
        }
        else{
          result = otpQsnService.getQsnsByType(txType);
          totalCount = result.size();
        }

        Map<String, Object> response = new HashMap<>();
        response.put("data", result);
        response.put("totalCount", totalCount);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/questionnaires/{txType}/{txId}")
    public ResponseEntity<OtpQsn> getQsnsByTypeId(@PathVariable("txType") String txType, @PathVariable("txId") String txId) {
        OtpQsn result = otpQsnService.getQsnsByTypeId(txType, txId);
        if (result == null) {
        return ResponseEntity.notFound().build();
        } else {
        return ResponseEntity.ok(result);
        }
    }

    @GetMapping("/questionnaires/{txType}/{txId}/tasks")
    public ResponseEntity<List<TaskBase>> getQsnTasks(@PathVariable("txType") String txType, @PathVariable("txId") String txId) {
        OtpQsn qsn = otpQsnService.getQsnsByTypeId(txType, txId);
        if (qsn == null) {
        return ResponseEntity.notFound().build();
        } else {
        return ResponseEntity.ok(taskSvcService.findTasks(qsn));
        }
    }

    @PostMapping("/questionnaires/{txType}/{txId}/createTask")
  public ResponseEntity<TaskBase> createTask(@PathVariable("txType") String txType, @PathVariable("txId") String txId) {
    if (!writable) {
      return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
    }
    OtpQsn qsn = otpQsnService.getQsnsByTypeId(txType, txId);
    if (qsn == null) {
      return ResponseEntity.notFound().build();
    } else {
      List<TaskBase> tasks = taskSvcService.findTasks(qsn);
      if (tasks.isEmpty()) {
        TaskBase task = taskSvcService.createTask(qsn);
        return ResponseEntity.ok(task);
      } else {
        TaskBase task = tasks.get(0);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(task);
      }
    }
  }

  @PostMapping("/questionnaires/{txType}/{txId}/price")
  public ResponseEntity<OtpQsn> updateQsnPriceByTypeId(Principal principal, @PathVariable("txType") String txType, @PathVariable("txId") String txId, @RequestBody String priceJson) {
    if (!writable) {
      return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
    }
    String userId = principal.getName();
    if (!taskSvcService.isMarketingUser(userId)) {
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }
    String price;
    try {
      price = new ObjectMapper().readTree(priceJson).asText();
    } catch (Exception e) {
      return ResponseEntity.badRequest().build();
    }
    if (otpQsnService.updateQsnPrice(txType, txId, price) == 0) {
      return ResponseEntity.notFound().build();
    } else {
      return getQsnsByTypeId(txType, txId);
    }
  }

  @PostMapping("/questionnaires/{txType}/{txId}/wwAccId")
  public ResponseEntity<?> updateQsnWwAccIdByTypeId(Principal principal, @PathVariable("txType") String txType, @PathVariable("txId") String txId, @RequestBody String wwAccIdJson) {
    if (!writable) {
      return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
    }
    String userId = principal.getName();
    if (!taskSvcService.isMarketingUser(userId)) {
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }
    String wwAccId;
    try {
      wwAccId = new ObjectMapper().readTree(wwAccIdJson).asText();
    } catch (Exception e) {
      return ResponseEntity.badRequest().build();
    }

    // Validate the wwAccId
    if(wwAccId.length() != VALID_WW_ACCT_ID_LENGTH){
      return ResponseEntity.badRequest().body(prepareErrorBody(WW_ACC_ID_INVALID_LENGTH_BODY));
    }

    if(!wwAccId.matches(DIGIT_REGEX) || wwAccId.equals(WwAccIdLookupApiService.INVALID_WW_ACCT_SOLD_TO) || wwAccId.startsWith(WwAccIdLookupApiService.INVALID_WW_ACCT_ID_PREFIX)){
      return ResponseEntity.badRequest().body(prepareErrorBody(wwAccId.concat(WW_ACCT_ID_INVALID_BODY_SUFFIX)));
    }

    OtpQsn qsn = otpQsnService.getQsnsByTypeId(txType, txId);
    // Check if task already exists for questionnaire and block editing
    List<TaskBase> qsnTasks = taskSvcService.findTasks(qsn);
    if(!qsnTasks.isEmpty()){
      return ResponseEntity.status(HttpStatus.CONFLICT)
              .body(prepareErrorBody(WW_ACC_ID_EDIT_CONFLICT_BODY));
    }
    if (otpQsnService.updateWwAccountId(txType, txId, wwAccId) == 0) {
      return ResponseEntity.notFound().build();
    } else {
      // create task
      taskSvcService.createTask(qsn);
      return getQsnsByTypeId(txType, txId);
    }
  }

  @GetMapping("/templateFunctions/{fnctName}")
  public ResponseEntity<List<String>> getTemplateFunctions(@PathVariable("fnctName") String fnctName) {
    List<String> userIds = taskSvcService.getFunctionUserIds(fnctName);
    return ResponseEntity.ok(userIds);
  }

  @GetMapping("/userContext")
  public ResponseEntity<UserContext> getUserContext(Principal principal) {
    String userId = principal.getName();
    UserContext context = new UserContext();
    context.setUserId(principal.getName());
    boolean isMarketingUser = taskSvcService.isMarketingUser(userId);
    context.setCanEditPrice(isMarketingUser);
    context.setCanEditWwAccId(isMarketingUser);
    return ResponseEntity.ok(context);
  }

  @PostMapping("/TPLD_CUSTOM_OPN/task/marketingReview/trigger/beforeTaskBranchTaken")
  public ResponseEntity<TaskSvcTriggerResponse> evaluateMarketingReviewProceedOrNotForTPLD(@RequestBody JsonNode reqBody){
    return ResponseEntity.ok(taskSvcService.evaluateMarketingReviewProceedOrNotForTPLD(reqBody));
  }
}
