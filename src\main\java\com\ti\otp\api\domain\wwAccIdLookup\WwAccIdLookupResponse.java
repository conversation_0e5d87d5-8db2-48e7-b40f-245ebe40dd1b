package com.ti.otp.api.domain.wwAccIdLookup;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WwAccIdLookupResponse {
    private String fullName;
    private String emailAddress;
    private String mytiCity;
    private String mytiCountry;
    private String mytiCompanyName;
    private String maaName;
    private String wwAcct;
    private String soldto;
    private String cpIdExists;
    private String wwAcctExists;
    private String directCustomer;
    private String custCity;
    private String custState;
    private String custCountry;
    private String contSoldtoCount;
    private String distributorList;
    private String topDistributorName;
    private String topDistributorWwacct;
}
