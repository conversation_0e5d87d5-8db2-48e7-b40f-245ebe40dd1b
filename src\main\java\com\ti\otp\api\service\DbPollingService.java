package com.ti.otp.api.service;

import java.util.Date;
import java.util.List;

import com.ti.otp.api.domain.KafkaErrorException;
import com.ti.otp.api.domain.UnknownException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.ti.otp.api.domain.OtpReq;
import com.ti.otp.api.kafka.KafkaMessageProducer;
import lombok.extern.slf4j.Slf4j;

import javax.mail.MessagingException;

@Service
@Slf4j
public class DbPollingService {
  @Value("${app.db-polling.enabled:false}")
  private boolean enabled;

  @Value("${app.db-polling.source-app}")
  private String srcApp;

  @Value("${app.db-polling.source-status}")
  private String srcStatus;

  @Autowired
  private OtpService otpService;

  @Autowired
  private KafkaMessageProducer kafkaMessageProducer;

  @Scheduled(fixedDelayString = "${app.db-polling.interval-ms}")
  public void schedulePollDb() throws MessagingException, UnknownException, KafkaErrorException {
    if (enabled) {
      log.info("Polling started: {}", new Date());
      List<OtpReq> reqs = otpService.getUnsentReqsByAppStatus(srcApp, srcStatus);
      for (OtpReq req : reqs) {
        log.info("Publishing: {}", req);
        kafkaMessageProducer.sendReq(req);
        otpService.markReqAsSent(req.getUuid());
      }
    }
  }
  
}
