package com.ti.otp.api.domain;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class OtpReq {
  @Hidden
  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private String uuid;

  private String initiatorId = "SYSTEM";
  private String initiatorName = "SYSTEM";

  @Schema(description = "Flow name", example = "TPLD")
  private String flowName;

  @Schema(description = "Request ID, typically a UUID string", example = "81e10546-6d77-4f45-917d-e05d6dc2de7b")
  private String txId;

  @Schema(description = "Request type", example = "TPLD_CUSTOM_OPN")
  private String txType;

  @Schema(description = "Source application name", example = "SAP")
  private String srcApp;

  @Length(max = 18, message = "length must be 18 characters or less")
  @Schema(description = "Original OPN", example = "TPLD1201RWB")
  private String opnOrig;

  @Length(max = 18, message = "length must be 18 characters or less")
  @Schema(description = "New OPN", example = "TPLD1201RWB123")
  private String opnNew;

  @Length(max = 14, message = "length must be 14 characters or less")
  @Schema(description = "New OPN's old material name", example = "TPLD1201RWB123")
  private String opnNewOldMatl;

  @Schema(description = "New OPN's GPN name", example = "TPLD1201")
  private String opnNewGpn;

  @Pattern(regexp = "Catalog|Custom", message = "must be either Catalog or Custom")
  @Schema(description = "Describes the type of OPN, namely Catalog or Custom", example = "Custom")
  private String opnNewType;

  @Schema(description = "Optional status message", example = "COMPLETE")
  private String status;

  @Schema(description = "Indicate success (true) or failure (false)", example = "true")
  private boolean success;

  @Schema(description = "Optional error message", example = "Sample error message")
  private String errMsg;

  @Schema(description = "Transaction data message including wwAccountId, customer email, price and OTP config string", example = "{\"wwAccountId\":\"**********\", \"customerEmail\":\"<EMAIL>\", \"price\":\"$0.56 USD\", \"otpCfg\":\":10000000400a0bbde538f00b9\n:10001000ec0e2000e003f03f00ff03003c200e9cac\n:10002000ed01112088558080558855140128211e26\n:10003000210a2100000917180300001100005a4b83\n:00000001FF\"}")
  private String txData;

  @Schema(description = "Optional transaction action message", example = "CLONE_OPN")
  private String txAction;

  @JsonIgnore
  private Date sentDate;

  @JsonIgnore
  private Date createdDate;

  @JsonIgnore
  public boolean isSent() {
    return sentDate != null;
  }
}
