package com.ti.otp.api.config;

import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.servers.Server;

@Component
public class SwaggerConfig {

  @Bean
  public OperationCustomizer customize() {
    return (operation, handlerMethod) -> operation
      .addParametersItem(
        new Parameter().in("header").required(true).description("Zuul secret header value").name("X-ZUUL-TI-SECRET"))
      .addParametersItem(
        new Parameter().in("header").required(true).description("Zuul user id value").name("X-ZUUL-TI-UID"));
  }

  @Bean
  public OpenAPI customOpenAPI() {
    return new OpenAPI()
      .addServersItem(new Server().url("http://localhost:8080"))
      .addServersItem(new Server().url("https://otp-api-dev.leokdd.itg.ti.com"))
      .addServersItem(new Server().url("https://otp-api.leokdp.itg.ti.com"))
      .info(new Info().title("OTP API").version("1.0"));
  }
}
