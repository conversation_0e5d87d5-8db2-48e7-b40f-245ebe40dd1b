<p>Please enter <b>Pricing Agreement (PA) information</b> to SAP.
    Click <a href="https://dms-dev.leokdd.itg.ti.com/tpld-ui/req/${ctxIdValue}">here</a> to get info on Price and WW Cust No.
    After completing the PA entries, click <a href="https://foundation-dev.itg.ti.com/tasksvc/app/tpld/task/${taskUuid}/assignment">here</a> to update task status.
</p>
<table>
    <tbody>
    <tr class="row-even"><td>OPN</td><td>${taskName}</td></tr>
    <tr class="row-odd"><td>Price</td><td>${(task.metaMap.price.attrValue)!}</td></tr>
    <tr class="row-even"><td>WW Cust No.</td><td>${(task.metaMap.wwAccountId.attrValue)!}</td></tr>
    <tr class="row-odd"><td>Customer Email</td><td>${(task.metaMap.customerEmail.attrValue)!}</td></tr>
    </tbody>
</table>
<br>
<p>Task information<p>
<table>
    <tbody>
    <tr class="row-even"><td>State</td><td>${stateName}</td></tr>
    <tr class="row-odd"><td>Assigned to</td><td>${(userName)!}</td></tr>
    <tr class="row-even"><td>Assigned at</td><td>${createdDttm?datetime}</td></tr>
    <tr class="row-odd"><td>Assigned function</td><td>${fnctName}</td></tr>
    </tbody>
</table>
<br>
<p>
    To view TPLD request,
    <a href="https://dms-dev.leokdd.itg.ti.com/tpld-ui/req/${ctxIdValue}">click here</a>.
</p>
<p>
    To view this task within Task Services,
    <a href="https://foundation-dev.itg.ti.com/tasksvc/app/tpld/task/${taskUuid}/assignment">click here</a>.
</p>