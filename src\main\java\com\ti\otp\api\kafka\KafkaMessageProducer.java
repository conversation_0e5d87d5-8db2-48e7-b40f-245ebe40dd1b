package com.ti.otp.api.kafka;

import com.ti.otp.api.config.ListenFailureEmailConfig;
import com.ti.otp.api.domain.KafkaErrorException;
import com.ti.otp.api.domain.UnknownException;
import com.ti.otp.api.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;
import javax.annotation.PostConstruct;
import javax.mail.MessagingException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import com.ti.otp.api.domain.OtpReq;

@Component
@Slf4j
public class KafkaMessageProducer {

  @Autowired
  private KafkaTemplate<String, Object> kafkaTemplate;

  @Autowired
  private EmailService emailService;

  @Autowired
  private ListenFailureEmailConfig listenFailureEmailConfig;

  @Value(value = "${app.kafka.enabled:false}")
  private Boolean enabled;

  @Value(value = "${app.kafka.topic.outbound}")
  private String topicOut;

  private TreeMap<String,String> appTopicOut = new TreeMap<>();
  private String defaultTopicOut = null;

  @PostConstruct
  public void init() {
    if (enabled) {
      if (!StringUtils.hasText(topicOut)) {
        log.warn("Kafka producer disabled due to no outbound topic");
      } else {
        log.info("Kafka producer enabled = {}", enabled);
        for (String item: topicOut.split(",")) {
          item = item.trim();
          if (item.contains(":")) {
            String[] parts = item.split(":");
            String app = parts[0];
            String topic = parts[1];
            appTopicOut.put(app, topic);
          } else {
            defaultTopicOut = item;
          }
        }
        if (!appTopicOut.isEmpty()) {
          for (Map.Entry<String,String> entry: appTopicOut.entrySet()) {
            log.info("Outbound topic for {} = {}", entry.getKey(), entry.getValue());
          }
          if (defaultTopicOut == null) {
            defaultTopicOut = appTopicOut.firstEntry().getValue();
          }
        }
        log.info("Default outbound topic = {}", defaultTopicOut);
      }
    }
  }

  public boolean isEnabled() {
    return Boolean.TRUE.equals(enabled);
  };

  public Collection<String> getSrcApps() {
    return appTopicOut.keySet();
  }

  public String sendReq(OtpReq req) throws UnknownException, KafkaErrorException, MessagingException {
    String srcApp = req.getSrcApp();
    String topic = appTopicOut.getOrDefault(srcApp, defaultTopicOut);
    sendReq(topic, req);
    return topic;
  }

  public void sendReq(String topicName, OtpReq req) throws KafkaErrorException, UnknownException, MessagingException {
    if (enabled) {
      try {
        log.info("Sending Kafka request to topic {}: {}", topicName, req);
        kafkaTemplate.send(topicName, req);
        log.info("Sent Kafka request to topic {}: {}", topicName, req);
      }

      catch (KafkaException e) {
        String msg = "Following Kafka error occurred while sending message to Kafka: " + e.getMessage();
        log.error(msg);
        emailService.send(msg, listenFailureEmailConfig);
        throw new KafkaErrorException(e.getMessage());
      }

      catch (Exception e) {
        String msg = "Following error occurred: " + e.toString();
        log.error(msg);
        emailService.send(msg, listenFailureEmailConfig);
        throw new UnknownException(e.toString());
      }
    }
  }

  public void sendStartTxReq(OtpReq req) throws UnknownException, KafkaErrorException, MessagingException {
    String srcApp = req.getSrcApp();
    String topic = appTopicOut.getOrDefault(srcApp, defaultTopicOut);
    sendReq(topic, req);
  }
}
