--liquibase formatted sql

--changeset a0866112:001
create table OTP_QSN (
  TX_TYPE varchar2(40) not null,
  TX_ID varchar2(40) not null,
  CREATED_DATE timestamp default systimestamp not null,
  QSN_DATA clob
);
--rollback drop table OTP_QSN;

--changeset a0866112:002
alter table OTP_QSN add constraint OTP_QSN_PK primary key (TX_TYPE, TX_ID);
--rollback alter table OTP_QSN drop constraint OTP_QSN_PK;

--changeset a0866112:003
alter table OTP_QSN add constraint OTP_QSN_CC_DATA check (QSN_DATA is json);
--rollback alter table OTP_QSN drop constraint OTP_QSN_CC_DATA;

--changeset a0866112:004
alter table OTP_QSN modify (QSN_DATA not null);
--rollback alter table OTP_QSN modify (QSN_DATA null);

--changeset a0866112:005
alter table OTP_QSN add (PRICE varchar2(20));
--rollback alter table OTP_QSN drop column PRICE;

--changeset a0866112:006
create table OTP_QSN_FNCT (
  FNCT_NAME varchar2(40) not null,
  USER_ID varchar2(40) not null
);
--rollback drop table OTP_QSN_FNCT;

--changeset a0866112:007
alter table OTP_QSN_FNCT add constraint OTP_QSN_FNCT_PK primary key (FNCT_NAME, USER_ID);
--rollback alter table OTP_QSN_FNCT drop constraint OTP_QSN_FNCT_PK;

--changeset a0866112:008
alter table OTP_QSN modify (PRICE default '$0.56 USD');
--rollback alter table OTP_QSN modify (PRICE default null);
