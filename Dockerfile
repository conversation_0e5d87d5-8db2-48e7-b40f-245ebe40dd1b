FROM artifactory.itg.ti.com/docker-public-local/library/openjdk:11 as builder
WORKDIR application
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

FROM artifactory.itg.ti.com/docker-public-local/library/openjdk:11
LABEL TI_IMAGE_SOURCE ="artifactory.itg.ti.com/docker-public-local/library/openjdk:11"

# run as non root user
RUN addgroup --system --gid 1001 app && adduser --system -uid 1001 --group app
USER app

# ensure timezone is dallas
ENV TZ='America/Chicago'

EXPOSE 8080
WORKDIR application

# build application layers. app code as last layer.
COPY --from=builder application/dependencies/ ./
COPY --from=builder application/spring-boot-loader/ ./
COPY --from=builder application/snapshot-dependencies/ ./
COPY --from=builder application/application/ ./

ENTRYPOINT ["java", "org.springframework.boot.loader.JarLauncher"]
