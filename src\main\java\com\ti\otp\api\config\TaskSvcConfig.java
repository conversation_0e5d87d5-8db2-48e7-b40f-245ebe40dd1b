package com.ti.otp.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ti.spring.boot.starter.apigee.client.TiApigeeWebClient;
import com.ti.tasksvc.client.invoker.ApiClient;
import com.ti.tasksvc.client.invoker.auth.ApiKeyAuth;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Configuration
@ConfigurationProperties(prefix = "app.tasksvc")
@Getter
@Setter
@Slf4j
public class TaskSvcConfig {

  private boolean enabled;
  private String basePath;
  private String tokenUrl;
  private String tokenClientId;
  private String tokenClientSecret;
  private String userId;
  private String tmplUuid;
  private String initBranch;
  private String marketingFunction;

  @Bean
  public ApiClient taskSvcWebClient() {
    log.info("Task Service integration enabled = {}", enabled);
    if (enabled) {
      log.info("Task Service Apigee server = {}", basePath);
    }
    TiApigeeWebClient webClient = new TiApigeeWebClient(basePath + tokenUrl, tokenClientId, tokenClientSecret);
    ApiClient apiClient = new ApiClient(webClient);
    apiClient.setBasePath(basePath);
    ApiKeyAuth apiuid = (ApiKeyAuth)apiClient.getAuthentication("apiuid");
    apiuid.setApiKey(userId);
    return apiClient;
  }

}
