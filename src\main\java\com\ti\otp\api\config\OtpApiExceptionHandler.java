package com.ti.otp.api.config;

import com.ti.otp.api.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Slf4j
@ControllerAdvice
public class OtpApiExceptionHandler {

    @ExceptionHandler(EloquaErrorException.class)
    public ResponseEntity<?> handleEloquaErrorException (EloquaErrorException e) {
        return ResponseEntity.status(HttpStatus.FAILED_DEPENDENCY)
                .body("Failed to process request, encountered the following error: " + e.getMessage());
    }

    @ExceptionHandler(InvalidTxIdException.class)
    public ResponseEntity<?> handleInvalidTxIdException(InvalidTxIdException e) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body("Failed to process request, encountered the following error: " + e.getMessage());
    }

    @ExceptionHandler(KafkaErrorException.class)
    public ResponseEntity<?> handleKafkaErrorException (KafkaErrorException e) {
        return ResponseEntity.status(HttpStatus.FAILED_DEPENDENCY)
                .body("Failed to process request, encountered the following error: " + e.getMessage());
    }

    @ExceptionHandler(DbErrorException.class)
    public ResponseEntity<?> handleDbErrorException (DbErrorException e) {
        return ResponseEntity.status(HttpStatus.FAILED_DEPENDENCY)
                .body("Failed to process request, encountered the following error: " + e.getMessage());
    }

    @ExceptionHandler(UnknownException.class)
    public ResponseEntity<?> handleUnknownException(UnknownException e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to process request, encountered the following error: " + e.getMessage());
    }
}
