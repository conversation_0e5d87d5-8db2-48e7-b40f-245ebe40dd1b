package com.ti.otp.api.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@ConfigurationProperties(prefix = "app.ww-acc-id.refresh.api")
@Slf4j
@Getter
@Setter
public class WwAccIdLookupApiConfig {
    private boolean enabled;
    private String url;
    private String username;
    private String password;

    @PostConstruct
    public void run(){
        log.info("WW Account ID lookup API enabled: {}", enabled);
        if(enabled){
            log.debug("WW Account ID lookup API URL: {}", url);
        }
    }
}
