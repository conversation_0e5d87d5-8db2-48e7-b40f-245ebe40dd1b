package com.ti.otp.api.service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.ti.otp.api.config.NewCustSetupEmailConfig;
import com.ti.otp.api.domain.EloquaEmail;
import com.ti.otp.api.domain.EloquaErrorException;
import com.ti.otp.api.domain.QsnUserCompanyInfo;
import com.ti.otp.api.domain.wwAccIdLookup.WwAccIdLookupResponse;
import com.ti.otp.api.util.JsonUtils;
import com.ti.otp.api.util.TemplateLoaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.ti.otp.api.domain.OtpQsn;
import com.ti.otp.api.repository.OtpQsnDao;

import lombok.extern.slf4j.Slf4j;

import javax.mail.MessagingException;

@Service
@Slf4j
public class OtpQsnService {

  private static final String USER_EMAIL_PATH = "/userInfo/email";
  private static final String USER_FIRST_NAME_PATH = "/userInfo/firstname";
  private static final String USER_LAST_NAME_PATH = "/userInfo/lastname";
  private static final String USER_COMPANY_NAME_PATH = "/order/questionnaire/companyName";
  private static final String USER_COUNTRY_NAME_PATH = "/order/questionnaire/companyCountry";
  private static final String TPLD_TX_TYPE = "TPLD_CUSTOM_OPN";
  private static final String ORDER_TYPE_PATH = "/order/questionnaire/orderType";
  private static final String ORDER_TYPE_SAMPLES = "samples";

  @Autowired
  private OtpQsnDao otpQsnDao;

  @Autowired
  private TaskSvcService taskSvcService;

  @Autowired
  private MyTiModelApiService myTiModelApiService;

  @Autowired
  private WwAccIdLookupApiService wwAccIdLookupApiService;
  @Autowired
  private EmailService emailService;
  @Autowired
  private NewCustSetupEmailConfig newCustSetupEmailConfig;
  @Autowired
  private TemplateLoaderUtil templateLoaderUtil;

  @Autowired
  private EloquaEmailService eloquaEmailService;

  @Value(value = "${eloqua.environment}")
  private String environment;

  @Value(value = "${eloqua.high-volume-business-days}")
  private String highVolumeBusinessDays;

  @Value(value = "${eloqua.customer-support-url}")
  private String customerSupportUrl;

  @Value(value = "${eloqua.approval-high-volume-event-id}")
  private String approvalHighVolumeEventId;

  @Value(value = "${eloqua.approval-high-volume-event-hash}")
  private String approvalHighVolumeEventHash;

  @Value("${app.default-tx-type:TPLD_CUSTOM_OPN}")
  private String defaultTxType;

  public void createQsn(OtpQsn qsn) throws MessagingException, EloquaErrorException {
    log.debug("Creating: {}", qsn);
    if (!StringUtils.hasLength(qsn.getTxType())) {
      qsn.setTxType(defaultTxType);
    }
    if (!StringUtils.hasLength(qsn.getTxId())) {
      qsn.setTxId(UUID.randomUUID().toString());
    }

    otpQsnDao.createQsn(qsn);

    String orderType = JsonUtils.getDataAt(qsn.getQsnData(), ORDER_TYPE_PATH);

    lookupAndAddWwAccIdAndCompanyName(qsn);

    // Send request receipt email to customer(Similar to Approve HV emails, which will go on request receipt now)
    EloquaEmail eloquaEmail = new EloquaEmail();
    eloquaEmail.setEmailTo(JsonUtils.getDataAt(qsn.getQsnData(), USER_EMAIL_PATH));
    eloquaEmail.setFirstName(JsonUtils.getDataAt(qsn.getQsnData(), USER_FIRST_NAME_PATH));
    eloquaEmail.setEnvironment(environment);
    eloquaEmail.setBusinessDays(highVolumeBusinessDays);
    eloquaEmail.setCustomerSupportUrl(customerSupportUrl);
    eloquaEmailService.sendEloquaMail(eloquaEmail, approvalHighVolumeEventId, approvalHighVolumeEventHash);

    if(ORDER_TYPE_SAMPLES.equals(orderType) || qsn.getWwAccountId() != null){
      createTask(qsn);
    }
  }

  private void createTask(OtpQsn qsn){
    if (taskSvcService.isEnabled()) {
      taskSvcService.createTask(qsn);
    }
  }

  public List<OtpQsn> getQsnsByType(String txType) {
    return otpQsnDao.getQsnsByType(txType);
  }

  public List<OtpQsn> getQsnsByTypePagination(String txType, int pageSize, int offset) {
    return otpQsnDao.getQsnsByTypePagination(txType, pageSize, offset);
  }

  public int getQsnsCountByType(String txType) {
    return otpQsnDao.getQsnsCountByType(txType);
  }

  public OtpQsn getQsnsByTypeId(String txType, String txId) {
    return otpQsnDao.getQsnsByTypeId(txType, txId);
  }

  public int updateQsnPrice(String txType, String txId, String price) {
    return otpQsnDao.updateQsnPrice(txType, txId, price);
  }

  public int updateWwAccountId(String txType, String txId, String wwAccId){
    return updateWwAccountIdAndCompanyName(txType, txId, wwAccId, null);
  }

  public int updateWwAccountIdAndCompanyName(String txType, String txId, String wwAccId, String companyName){
    return otpQsnDao.updateQsnWwAccountIdAndCompanyName(txType, txId, wwAccId, companyName);
  }

  // PRISM and SAP both need this information
  private void lookupAndAddWwAccIdAndCompanyName(OtpQsn qsn){
    String userEmail = JsonUtils.getDataAt(qsn.getQsnData(), USER_EMAIL_PATH);
    if(!StringUtils.hasLength(userEmail)){
      log.warn("No user email found in the questionnaire at path: {}", USER_EMAIL_PATH);
      return;
    }

    // Lookup customer information using API
    WwAccIdLookupResponse res = wwAccIdLookupApiService.callApi(userEmail);
    if(res == null){
      log.debug("No data found for userEmail={} via WW Account ID Lookup API", userEmail);
      return;
    }

    // If valid WW Account ID, use that, or use if valid Sold To use that
    String wwAccId = null;
    if(wwAccIdLookupApiService.isValidWwAccId(res.getWwAcct())){
      wwAccId = res.getWwAcct();
    }
    else if(wwAccIdLookupApiService.isValidSoldTo(res.getSoldto())){
      wwAccId = res.getSoldto();
    }

    // SAP and PRISM dependent on WW Account ID
    if(wwAccId == null){
      log.debug("No WW Account ID found for user email: {}", userEmail);
      return;
    }
    log.debug("WW Account ID found (userEmail={}, wwAccountId={})", userEmail, wwAccId);

    // PRISM dependent on Company Name
    if(!StringUtils.hasLength(res.getMytiCompanyName())){
      log.debug("No Company Name found for user email: {}", userEmail);
      // TODO: Hold task creation?
    }
    else{
      log.debug("Company name found (userEmail={}, companyName={})", userEmail, res.getMytiCompanyName());
    }

    updateWwAccountIdAndCompanyName(qsn.getTxType(), qsn.getTxId(), wwAccId, res.getMytiCompanyName());
    qsn.setWwAccountId(wwAccId);
    qsn.setCompanyName(res.getMytiCompanyName());
  }


  private QsnUserCompanyInfo getUserCompanyInfoFromQsn(OtpQsn qsn){
    QsnUserCompanyInfo info = new QsnUserCompanyInfo();
    info.setEmail(JsonUtils.getDataAt(qsn.getQsnData(), USER_EMAIL_PATH));
    info.setFirstName(JsonUtils.getDataAt(qsn.getQsnData(), USER_FIRST_NAME_PATH));
    info.setLastName(JsonUtils.getDataAt(qsn.getQsnData(), USER_LAST_NAME_PATH));
    info.setCompanyName(JsonUtils.getDataAt(qsn.getQsnData(), USER_COMPANY_NAME_PATH));
    info.setCompanyCountry(JsonUtils.getDataAt(qsn.getQsnData(), USER_COUNTRY_NAME_PATH));

    return info;
  }

  private void sendNewCustSetupEmail(List<QsnUserCompanyInfo> infoList) throws IOException, MessagingException {
    if(!newCustSetupEmailConfig.isEnabled()){
      return;
    }

    if(infoList.isEmpty()){
      return;
    }

    // Prepare email body
    String emailBody = templateLoaderUtil.readAsString(newCustSetupEmailConfig.getTemplate());
    StringBuilder htmlRows = new StringBuilder();
    infoList.forEach(info -> {
      htmlRows.append("<tr>");
      htmlRows.append("<td>").append(info.getEmail()).append("</td>");
      htmlRows.append("<td>").append(info.getFirstName()).append("</td>");
      htmlRows.append("<td>").append(info.getLastName()).append("</td>");
//      htmlRows.append("<td>").append(info.getCompanyName()).append("</td>");
//      htmlRows.append("<td>").append(info.getCompanyCountry()).append("</td>");
      htmlRows.append("</tr>");
    });
    emailBody = emailBody.replaceAll("\\{tableRows\\}", htmlRows.toString());

    emailService.send(emailBody, newCustSetupEmailConfig);
  }

  /**
   * Attempt to refresh WW Account ID for questionnaires without it
   * Steps:
   * Query all questionnaires (except for the samples) without WW Acc ID
   * Lookup for IDs for all questionnaires and add the ones found
   * Create Marketing Review task for all the refreshed ones
   * For the missing ones, send email for populating, based on last follow-up date
   */
  @Scheduled(cron = "${app.ww-acc-id.refresh.cron.expression}")
  public void executeRefresh() throws MessagingException, IOException{
    log.debug("WW Account ID refresh started: {}", new Date());

    // Lookup Missing WW Account IDs for applicable questionnaires only for the non samples i.e. prod
    List<OtpQsn> qsns = otpQsnDao.getQsnsWithoutWwAccIdByType(TPLD_TX_TYPE);
    qsns.forEach(this::lookupAndAddWwAccIdAndCompanyName);

    // Create Marketing Review task only for the non samples i.e. prod
    qsns.stream()
        .filter(qsn -> qsn.getWwAccountId() != null)
        .forEach(this::createTask);

    // Send emails for the ones that couldn't be refreshed and follow up required (except for samples)
    qsns = otpQsnDao.getQsnsWithoutWwAccIdAndRequiringFollowUpByType(TPLD_TX_TYPE, newCustSetupEmailConfig.getDaysBeforeFollowUp());
    // Send onboarding email only for the distinct user emails
    Set<String> distinctUserEmails = new HashSet<>();
    List<QsnUserCompanyInfo> userCompanyInfoWoWwAccId = qsns.stream()
            .map(this::getUserCompanyInfoFromQsn)
            .filter(info -> distinctUserEmails.add(info.getEmail()))
            .collect(Collectors.toList());

    userCompanyInfoWoWwAccId.forEach(info -> log.debug("New customer setup required for user: {}",info.toString()));
    // Send email for new customer WW Account ID setup
    sendNewCustSetupEmail(userCompanyInfoWoWwAccId);
    // Update last follow-up date in the questionnaires
    qsns.forEach(qsn -> otpQsnDao.updateQsnWwAccIdFollowUpDate(TPLD_TX_TYPE, qsn.getTxId()));

    log.debug("WW Account ID refresh complete: {}", new Date());
  }
}
