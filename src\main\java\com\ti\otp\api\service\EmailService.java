package com.ti.otp.api.service;

import com.ti.otp.api.config.GenericEmailConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;


@Service
@Slf4j
public class EmailService {

    @Autowired
    private JavaMailSender javaMailSender;

    public void send(String emailBody, GenericEmailConfig genericEmailConfig) throws MessagingException {

        if(!genericEmailConfig.isEnabled())
        {
            return;
        }

        log.debug("send(emailBody:{})", emailBody);

        // send the email
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message);
        helper.setFrom(genericEmailConfig.getFrom());

        if(!genericEmailConfig.getTos().isEmpty()) {
            helper.setTo(genericEmailConfig.getTos().split(","));
        }

        helper.setSubject(genericEmailConfig.getSubject());
        helper.setText(emailBody, true);

        javaMailSender.send(message);
        log.debug("mail sent!");
    }
}
